"""
通知弹窗模块
用于显示飞书消息提醒弹窗
"""
import threading
import tkinter as tk
from tkinter import messagebox
from loguru import logger

class NotificationDialog:
    """通知弹窗类"""
    
    def __init__(self):
        self.dialog_open = False
        self.result = None
    
    def show_notification(self, title="飞书消息提醒", message="您有新的飞书消息！\n点击确定停止音乐提醒"):
        """显示通知弹窗"""
        if self.dialog_open:
            logger.warning("弹窗已经打开")
            return
            
        self.dialog_open = True
        self.result = None
        
        try:
            # 创建主窗口（隐藏）
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            
            # 设置窗口属性
            root.attributes('-topmost', True)  # 置顶显示
            root.lift()
            root.focus_force()
            
            logger.info("显示通知弹窗")
            
            # 显示消息框
            result = messagebox.showinfo(
                title=title,
                message=message,
                parent=root
            )
            
            self.result = result
            logger.info("用户点击了确定按钮")
            
        except Exception as e:
            logger.error(f"显示弹窗时出错: {str(e)}")
        finally:
            try:
                root.destroy()
            except:
                pass
            self.dialog_open = False
    
    def show_notification_async(self, title="飞书消息提醒", message="您有新的飞书消息！\n点击确定停止音乐提醒", callback=None):
        """异步显示通知弹窗"""
        def show_dialog():
            self.show_notification(title, message)
            if callback:
                callback()
        
        dialog_thread = threading.Thread(target=show_dialog, daemon=True)
        dialog_thread.start()
        return dialog_thread

class MusicNotification:
    """音乐通知类 - 本地播放模式"""

    def __init__(self, music_player):
        self.music_player = music_player
        self.is_active = False

    def start_music_notification(self, music_file, title="飞书消息提醒", message="您有新的飞书消息！"):
        """开始音乐提醒（本地播放）"""
        logger.info(f"启动本地音乐播放: {music_file}")

        # 直接启动音乐播放
        self.music_player.start_playing(music_file)
        self.is_active = True

    def stop_music_notification(self):
        """停止音乐提醒"""
        if not self.is_active:
            return

        logger.info("停止音乐播放")
        self.is_active = False

        # 直接停止音乐播放
        self.music_player.stop_playing()

    def is_notification_active(self):
        """检查音乐提醒是否激活"""
        return self.is_active

class CombinedNotification:
    """组合通知类 - 音乐播放 + 弹窗提醒"""

    def __init__(self, music_player):
        self.music_player = music_player
        self.dialog = NotificationDialog()
        self.is_active = False

    def start_notification(self, music_file, title="飞书消息提醒", message="您有新的飞书消息！\n点击确定停止音乐提醒"):
        """启动组合通知：音乐播放 + 弹窗"""
        if self.is_active:
            logger.warning("通知已经激活")
            return

        logger.info(f"启动组合通知: 音乐播放 + 弹窗")
        self.is_active = True

        # 1. 启动音乐播放（循环播放）
        self.music_player.start_playing(music_file)
        logger.info("音乐开始循环播放")

        # 2. 显示弹窗（异步，带回调）
        def on_dialog_close():
            """弹窗关闭时的回调函数"""
            logger.info("用户点击了确定按钮，停止音乐播放")
            self.stop_notification()

        self.dialog.show_notification_async(title, message, on_dialog_close)
        logger.info("弹窗已显示，等待用户点击")

    def stop_notification(self):
        """停止组合通知"""
        if not self.is_active:
            return

        logger.info("停止组合通知")
        self.is_active = False

        # 停止音乐播放
        self.music_player.stop_playing()

    def is_notification_active(self):
        """检查通知是否激活"""
        return self.is_active

# 创建全局通知实例
def create_music_notification(music_player):
    """创建音乐通知实例（仅音乐）"""
    return MusicNotification(music_player)

def create_combined_notification(music_player):
    """创建组合通知实例（音乐 + 弹窗）"""
    return CombinedNotification(music_player)
