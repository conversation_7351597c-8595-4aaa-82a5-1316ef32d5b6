# ================================================================
# LarkFlow 配置文件
# ================================================================

# 飞书认证配置 (必需)
LARK_COOKIE="YOUR_LARK_COOKIE_HERE"

# 消息匹配和回复配置
# 匹配模式: content(内容匹配) 或 group_name(群聊名称匹配)
MESSAGE_MATCH_MODE="content"

# 内容匹配模式配置
TRIGGER_PATTERN="已接通人工.*?@.*?为你服务.*?请问.*?帮你"
REPLY_MESSAGE="您好！有什么可以帮您？"

# 群聊名称匹配模式配置
GROUP_NAME_PATTERN=".*'s 门店IT服务台"
GROUP_NAME_CONTENT_PATTERN="^None$"
GROUP_NAME_REPLY_MESSAGE="您好！我是IT服务台智能助手，有什么可以帮您？"

# 音乐提醒配置
ENABLE_MUSIC_NOTIFICATION=true
NOTIFICATION_MUSIC_FILE="static/audio/notification.mp3"
NOTIFICATION_TITLE="飞书消息提醒"
NOTIFICATION_MESSAGE="您有新的飞书消息！\n点击确定停止音乐提醒"

# 电话提醒配置
ENABLE_PHONE_NOTIFICATION=true
LARK_APP_ID="your_app_id_here"
LARK_APP_SECRET="your_app_secret_here"
PHONE_NOTIFICATION_USER_IDS="user_id_1,user_id_2"
PHONE_NOTIFICATION_MESSAGE_IDS="msg_id_1,msg_id_2,msg_id_3,msg_id_4"
PHONE_NOTIFICATION_INTERVAL=15

# Web管理界面配置
WEB_HOST=127.0.0.1
WEB_PORT=8080
WEB_DEBUG=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/larkagentx.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5
