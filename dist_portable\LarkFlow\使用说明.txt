# LarkFlow 便携版使用指南

## 🚀 快速开始

### Windows用户
1. **GUI版本（推荐）**: 双击 `启动GUI版本.bat`
2. **命令行版本**: 双击 `启动命令行版本.bat`

### Linux/Mac用户
1. 打开终端，进入程序目录
2. 运行: `./start.sh`
3. 根据提示选择运行模式

## ⚙️ 配置说明

### 首次使用
1. 程序会自动创建 `.env` 配置文件
2. 编辑 `.env` 文件，填入必要配置：
   - `LARK_COOKIE`: 飞书Cookie（必需）
   - 其他配置项根据需要修改

### 获取飞书Cookie
1. 打开浏览器，登录飞书网页版
2. 按F12打开开发者工具
3. 切换到Network标签页
4. 刷新页面，找到任意请求
5. 复制Cookie值到配置文件

## 📁 目录结构

```
LarkFlow/
├── LarkFlow-GUI.exe          # GUI版本可执行文件
├── LarkFlow-CLI.exe          # 命令行版本可执行文件
├── 启动GUI版本.bat           # Windows GUI启动脚本
├── 启动命令行版本.bat        # Windows CLI启动脚本
├── start.sh                  # Linux/Mac启动脚本
├── .env                      # 配置文件
├── .env.example              # 配置模板
├── static/                   # 静态资源
│   ├── audio/               # 音频文件目录
│   └── lark_decrypt.js      # 飞书解密脚本
├── logs/                     # 日志目录（自动创建）
└── 使用说明.txt              # 本文件
```

## 🎵 音乐提醒设置

1. 将音乐文件放入 `static/audio/` 目录
2. 支持格式：MP3、WAV、OGG
3. 在 `.env` 文件中设置音乐文件路径：
   ```
   NOTIFICATION_MUSIC_FILE=static/audio/your_music.mp3
   ```

## � Node.js集成说明

本程序采用"JavaScript优先，Python备用"的智能兼容性方案：

### ✅ 优势
- **完美兼容**: 在任何环境下都能正常工作
- **零依赖**: 无需安装Node.js或其他外部软件
- **自动降级**: 智能检测并选择最佳执行方式
- **体积精简**: 无需打包额外的运行时环境

### � 工作原理
- 优先尝试使用系统JavaScript引擎（如果可用）
- 遇到兼容性问题时自动切换到Python实现
- 确保所有核心功能在任何环境下都能正常工作

## �🔧 常见问题

### 1. 程序无法启动
- 检查是否有杀毒软件拦截
- 确认配置文件格式正确
- 查看日志文件获取详细错误信息

### 2. 无法连接飞书
- 检查网络连接
- 确认Cookie是否有效（可能已过期）
- 重新获取Cookie并更新配置

### 3. 音乐无法播放
- 确认音频文件存在且格式支持
- 检查系统音频设备是否正常
- 确认音频文件路径配置正确

### 4. JavaScript执行错误
- 程序会自动使用Python备用方案，通常不会出现此问题
- 如果看到"尝试使用Python实现的备用方案"日志，说明自动降级正常工作
- 查看日志文件获取详细错误信息

### 5. 权限问题（Linux/Mac）
```bash
# 给予执行权限
chmod +x start.sh
chmod +x LarkFlow-GUI
chmod +x LarkFlow-CLI
```

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: GitHub Issues
- 📖 详细文档: 查看docs目录

## 📄 版本信息

- 版本: 2.0.0
- 构建时间: 2025-07-31 15:01:30
- 开发者: LarkFlow Team

---

⭐ 如果这个工具对您有帮助，请给项目一个Star！
