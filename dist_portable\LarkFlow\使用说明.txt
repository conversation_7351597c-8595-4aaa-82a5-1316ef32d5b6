# LarkFlow 便携版使用指南

## 🚀 快速开始

### Windows用户
1. **GUI版本（推荐）**: 双击 `启动GUI版本.bat`
2. **命令行版本**: 双击 `启动命令行版本.bat`

### Linux/Mac用户
1. 打开终端，进入程序目录
2. 运行: `./start.sh`
3. 根据提示选择运行模式

## ⚙️ 配置说明

### 首次使用
1. 程序会自动创建 `.env` 配置文件
2. 编辑 `.env` 文件，填入必要配置：
   - `LARK_COOKIE`: 飞书Cookie（必需）
   - 其他配置项根据需要修改

### 获取飞书Cookie
1. 打开浏览器，登录飞书网页版
2. 按F12打开开发者工具
3. 切换到Network标签页
4. 刷新页面，找到任意请求
5. 复制Cookie值到配置文件

## 📁 目录结构

```
LarkFlow/
├── LarkFlow-GUI.exe          # GUI版本可执行文件
├── LarkFlow-CLI.exe          # 命令行版本可执行文件
├── 启动GUI版本.bat           # Windows GUI启动脚本
├── 启动命令行版本.bat        # Windows CLI启动脚本
├── start.sh                  # Linux/Mac启动脚本
├── nodejs/                   # Node.js便携版（自动集成）
│   └── node.exe             # Node.js执行文件
├── .env                      # 配置文件
├── .env.example              # 配置模板
├── static/                   # 静态资源
│   ├── audio/               # 音频文件目录
│   └── lark_decrypt.js      # 飞书解密脚本
├── docs/                     # 文档目录
├── logs/                     # 日志目录（自动创建）
└── 使用说明.txt              # 本文件
```

## 🎵 音乐提醒设置

1. 将音乐文件放入 `static/audio/` 目录
2. 支持格式：MP3、WAV、OGG
3. 在 `.env` 文件中设置音乐文件路径：
   ```
   NOTIFICATION_MUSIC_FILE=static/audio/your_music.mp3
   ```

## � Node.js集成说明

本程序已自动集成Node.js便携版，无需用户单独安装：

### ✅ 优势
- **完美兼容**: 解决不同系统环境下的JavaScript执行问题
- **开箱即用**: 无需安装Node.js，程序自带完整运行环境
- **性能最佳**: 使用Node.js引擎，执行效率最高
- **跨平台**: 支持Windows、Linux、Mac系统

### 📁 集成内容
- Node.js v18.20.4 LTS版本
- 仅包含核心运行文件，体积优化
- 自动配置环境变量，优先使用内置版本

## �🔧 常见问题

### 1. 程序无法启动
- 检查是否有杀毒软件拦截
- 确认配置文件格式正确
- 查看日志文件获取详细错误信息

### 2. 无法连接飞书
- 检查网络连接
- 确认Cookie是否有效（可能已过期）
- 重新获取Cookie并更新配置

### 3. 音乐无法播放
- 确认音频文件存在且格式支持
- 检查系统音频设备是否正常
- 确认音频文件路径配置正确

### 4. JavaScript执行错误
- 程序已集成Node.js，通常不会出现此问题
- 如仍有问题，请检查nodejs目录是否完整
- 查看日志文件获取详细错误信息

### 5. 权限问题（Linux/Mac）
```bash
# 给予执行权限
chmod +x start.sh
chmod +x LarkFlow-GUI
chmod +x LarkFlow-CLI
chmod +x nodejs/node
```

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: GitHub Issues
- 📖 详细文档: 查看docs目录

## 📄 版本信息

- 版本: 2.0.0
- 构建时间: 2025-07-31 14:06:28
- 开发者: LarkFlow Team

---

⭐ 如果这个工具对您有帮助，请给项目一个Star！
