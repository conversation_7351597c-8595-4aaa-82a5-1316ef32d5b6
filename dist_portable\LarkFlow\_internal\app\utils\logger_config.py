"""
统一的日志配置模块
确保所有启动方式使用相同的日志配置，解决日志不同步问题
"""
import sys
import os
from pathlib import Path
from loguru import logger


def setup_unified_logging(app_name="LarkFlow"):
    """
    设置统一的日志配置
    
    Args:
        app_name: 应用名称，用于日志标识
    """
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加控制台日志 - 简洁美观的输出格式（仅在控制台可用时）
    if sys.stdout is not None:
        logger.add(
            sys.stdout,
            format="<dim>{time:MM-DD HH:mm:ss}</dim> <level>[{level:^5}]</level> {message}",
            level="INFO",
            enqueue=True,  # 多线程安全
            backtrace=True,  # 更好的错误追踪
            diagnose=True   # 详细的错误诊断
        )
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 添加文件日志 - 统一使用 app.log
    logger.add(
        "logs/app.log",
        format="{time:YYYY-MM-DD HH:mm:ss} [{level:^5}] {message}",
        level="DEBUG",
        rotation="10 MB",
        retention="7 days",
        compression="zip",
        enqueue=True,  # 多线程安全
        backtrace=True,
        diagnose=True
    )
    
    # 添加错误日志文件 - 只记录错误和警告
    logger.add(
        "logs/error.log",
        format="{time:YYYY-MM-DD HH:mm:ss} [{level:^5}] {message}",
        level="WARNING",
        rotation="5 MB",
        retention="30 days",
        compression="zip",
        enqueue=True,
        backtrace=True,
        diagnose=True
    )
    
    logger.info(f"{app_name} 日志系统初始化完成")
    logger.info(f"日志文件: logs/app.log, logs/error.log")


def get_log_file_path():
    """获取主日志文件路径"""
    return Path("logs/app.log")


def get_error_log_file_path():
    """获取错误日志文件路径"""
    return Path("logs/error.log")


def flush_logs():
    """强制刷新所有日志到文件"""
    # loguru会自动处理刷新，但我们可以通过重新配置来确保立即写入
    pass


def test_logging():
    """测试日志功能"""
    logger.debug("这是一条调试信息")
    logger.info("这是一条信息")
    logger.warning("这是一条警告")
    logger.error("这是一条错误信息")
    logger.success("日志测试完成")
