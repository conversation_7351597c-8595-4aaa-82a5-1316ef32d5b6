"""
音频管理页面
音频文件管理、上传、选择、测试播放功能
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QListWidget, QListWidgetItem,
    QFrame, QMessageBox, QFileDialog, QProgressBar,
    QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from PyQt6.QtGui import QFont
from loguru import logger
import os
import shutil
from pathlib import Path

from app.core.config_manager import config_manager
from app.core.state_manager import state_manager


class AudioPage(QWidget):
    """音频管理页面"""
    
    def __init__(self):
        super().__init__()
        self.audio_dir = Path("static/audio")
        self.audio_dir.mkdir(parents=True, exist_ok=True)
        self.init_ui()
        self.load_audio_files()
        logger.info("音频管理页面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("音频管理")
        title_label.setObjectName("title")
        layout.addWidget(title_label)
        
        # 创建当前配置区域
        self.create_current_config(layout)
        
        # 创建文件管理区域
        self.create_file_management(layout)
        
        # 创建控制按钮区域
        self.create_control_buttons(layout)
    
    def create_current_config(self, parent_layout):
        """创建当前配置区域"""
        config_group = QGroupBox("当前音频配置")
        config_layout = QVBoxLayout(config_group)
        
        # 当前音频文件
        current_layout = QHBoxLayout()
        current_layout.addWidget(QLabel("当前音频文件:"))
        
        self.current_file_label = QLabel("未设置")
        self.current_file_label.setStyleSheet("color: #6c757d; font-style: italic;")
        current_layout.addWidget(self.current_file_label)
        current_layout.addStretch()
        
        # 测试播放按钮
        self.test_play_button = QPushButton("测试播放")
        self.test_play_button.setMinimumHeight(32)
        self.test_play_button.clicked.connect(self.test_play_audio)
        current_layout.addWidget(self.test_play_button)

        # 停止播放按钮
        self.stop_play_button = QPushButton("停止播放")
        self.stop_play_button.setObjectName("danger")
        self.stop_play_button.setMinimumHeight(32)
        self.stop_play_button.clicked.connect(self.stop_play_audio)
        current_layout.addWidget(self.stop_play_button)
        
        config_layout.addLayout(current_layout)
        
        # 音频状态
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("音乐提醒状态:"))
        
        self.music_status_label = QLabel("未知")
        status_layout.addWidget(self.music_status_label)
        status_layout.addStretch()
        
        config_layout.addLayout(status_layout)
        
        parent_layout.addWidget(config_group)
    
    def create_file_management(self, parent_layout):
        """创建文件管理区域"""
        file_group = QGroupBox("音频文件管理")
        file_layout = QVBoxLayout(file_group)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(200)
        self.file_list.itemDoubleClicked.connect(self.on_file_double_clicked)
        file_layout.addWidget(self.file_list)
        
        # 文件操作按钮
        file_button_layout = QHBoxLayout()
        
        # 上传文件按钮
        upload_button = QPushButton("上传音频文件")
        upload_button.setObjectName("success")
        upload_button.setMinimumHeight(32)
        upload_button.clicked.connect(self.upload_audio_file)
        file_button_layout.addWidget(upload_button)

        # 选择文件按钮
        select_button = QPushButton("选择为当前音频")
        select_button.setMinimumHeight(32)
        select_button.clicked.connect(self.select_audio_file)
        file_button_layout.addWidget(select_button)

        # 删除文件按钮
        delete_button = QPushButton("删除文件")
        delete_button.setObjectName("danger")
        delete_button.setMinimumHeight(32)
        delete_button.clicked.connect(self.delete_audio_file)
        file_button_layout.addWidget(delete_button)
        
        file_button_layout.addStretch()
        
        file_layout.addLayout(file_button_layout)
        parent_layout.addWidget(file_group)
    
    def create_control_buttons(self, parent_layout):
        """创建控制按钮区域"""
        control_layout = QHBoxLayout()
        
        # 刷新列表按钮
        refresh_button = QPushButton("刷新列表")
        refresh_button.setMinimumHeight(32)
        refresh_button.clicked.connect(self.load_audio_files)
        control_layout.addWidget(refresh_button)

        # 打开音频目录按钮
        open_dir_button = QPushButton("打开音频目录")
        open_dir_button.setMinimumHeight(32)
        open_dir_button.clicked.connect(self.open_audio_directory)
        control_layout.addWidget(open_dir_button)
        
        control_layout.addStretch()
        
        parent_layout.addLayout(control_layout)
        parent_layout.addStretch()
    
    def load_audio_files(self):
        """加载音频文件列表"""
        try:
            self.file_list.clear()
            
            # 支持的音频格式
            audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac']
            
            # 扫描音频目录
            if self.audio_dir.exists():
                for file_path in self.audio_dir.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in audio_extensions:
                        item = QListWidgetItem(file_path.name)
                        item.setData(Qt.ItemDataRole.UserRole, str(file_path))
                        
                        # 先添加到列表
                        self.file_list.addItem(item)

                        # 检查是否为当前选中的文件
                        current_file = config_manager.get_config('NOTIFICATION_MUSIC_FILE')
                        if current_file and file_path.name in current_file:
                            # 设置背景色和特殊标记
                            from PyQt6.QtGui import QBrush, QColor
                            item.setBackground(QBrush(QColor(0, 123, 255, 50)))  # 浅蓝色背景
                            item.setText(f"★ {file_path.name}")
                            item.setToolTip("当前选中的音频文件")
            
            # 更新当前配置显示
            self.update_current_config()
            
            logger.info(f"加载了 {self.file_list.count()} 个音频文件")
            
        except Exception as e:
            logger.error(f"加载音频文件失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"加载音频文件失败: {str(e)}")
    
    def update_current_config(self):
        """更新当前配置显示"""
        try:
            current_file = config_manager.get_config('NOTIFICATION_MUSIC_FILE')
            if current_file and os.path.exists(current_file):
                file_name = os.path.basename(current_file)
                self.current_file_label.setText(file_name)
                self.current_file_label.setStyleSheet("color: #28a745; font-weight: 500;")
                self.test_play_button.setEnabled(True)
            else:
                self.current_file_label.setText("未设置或文件不存在")
                self.current_file_label.setStyleSheet("color: #dc3545; font-style: italic;")
                self.test_play_button.setEnabled(False)
            
            # 更新音乐提醒状态
            music_enabled = state_manager.get_state("music_notification_enabled")
            if music_enabled:
                self.music_status_label.setText("已启用")
                self.music_status_label.setStyleSheet("color: #28a745; font-weight: 500;")
            else:
                self.music_status_label.setText("已禁用")
                self.music_status_label.setStyleSheet("color: #dc3545; font-weight: 500;")
            
        except Exception as e:
            logger.error(f"更新当前配置显示失败: {str(e)}")
    
    def upload_audio_file(self):
        """上传音频文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择音频文件",
                "",
                "音频文件 (*.mp3 *.wav *.ogg *.m4a *.aac);;所有文件 (*)"
            )
            
            if file_path:
                source_path = Path(file_path)
                target_path = self.audio_dir / source_path.name
                
                # 检查文件是否已存在
                if target_path.exists():
                    reply = QMessageBox.question(
                        self,
                        "文件已存在",
                        f"文件 {source_path.name} 已存在，是否覆盖？",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )
                    
                    if reply != QMessageBox.StandardButton.Yes:
                        return
                
                # 复制文件
                shutil.copy2(source_path, target_path)
                
                # 刷新文件列表
                self.load_audio_files()
                
                QMessageBox.information(self, "成功", f"音频文件 {source_path.name} 上传成功！")
                logger.info(f"音频文件上传成功: {target_path}")
        
        except Exception as e:
            logger.error(f"上传音频文件失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"上传音频文件失败: {str(e)}")
    
    def select_audio_file(self):
        """选择音频文件为当前音频"""
        try:
            current_item = self.file_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "提示", "请先选择一个音频文件")
                return
            
            file_path = current_item.data(Qt.ItemDataRole.UserRole)
            relative_path = f"static/audio/{os.path.basename(file_path)}"
            
            # 更新配置
            config_manager.set_config('NOTIFICATION_MUSIC_FILE', relative_path)
            config_manager.save_config()
            
            # 重新加载应用配置
            try:
                from app.config.settings import settings
                settings.reload()
            except Exception as e:
                logger.warning(f"重新加载应用配置失败: {str(e)}")
            
            # 刷新显示
            self.load_audio_files()
            
            QMessageBox.information(self, "成功", f"已选择 {os.path.basename(file_path)} 为当前音频文件")
            logger.info(f"音频文件选择成功: {relative_path}")
        
        except Exception as e:
            logger.error(f"选择音频文件失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"选择音频文件失败: {str(e)}")
    
    def delete_audio_file(self):
        """删除音频文件"""
        try:
            current_item = self.file_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "提示", "请先选择一个音频文件")
                return
            
            file_path = current_item.data(Qt.ItemDataRole.UserRole)
            file_name = os.path.basename(file_path)
            
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除音频文件 {file_name} 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                os.remove(file_path)
                self.load_audio_files()
                
                QMessageBox.information(self, "成功", f"音频文件 {file_name} 删除成功")
                logger.info(f"音频文件删除成功: {file_path}")
        
        except Exception as e:
            logger.error(f"删除音频文件失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"删除音频文件失败: {str(e)}")
    
    def on_file_double_clicked(self, item):
        """文件双击事件"""
        self.select_audio_file()
    
    def test_play_audio(self):
        """测试播放音频"""
        try:
            from app.utils.music_player import music_player
            from app.config.settings import settings
            
            # 检查音乐提醒开关
            if not state_manager.get_state("music_notification_enabled"):
                QMessageBox.warning(self, "提示", "音乐提醒功能已禁用，请先在状态概览页面启用")
                return
            
            # 获取当前音乐文件
            music_file = settings.get_current_music_file()
            
            if not os.path.exists(music_file):
                QMessageBox.warning(self, "错误", f"音频文件不存在: {music_file}")
                return
            
            # 开始播放
            music_player.start_playing(music_file)
            
            QMessageBox.information(self, "提示", "音频测试播放已开始，请检查是否有声音")
            logger.info(f"测试播放音频: {music_file}")
        
        except Exception as e:
            logger.error(f"测试播放音频失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"测试播放音频失败: {str(e)}")
    
    def stop_play_audio(self):
        """停止播放音频"""
        try:
            from app.utils.music_player import music_player
            music_player.stop_playing()
            
            QMessageBox.information(self, "提示", "音频播放已停止")
            logger.info("停止音频播放")
        
        except Exception as e:
            logger.error(f"停止音频播放失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"停止音频播放失败: {str(e)}")
    
    def open_audio_directory(self):
        """打开音频目录"""
        try:
            import subprocess
            import platform
            
            audio_path = str(self.audio_dir.absolute())
            
            if platform.system() == "Windows":
                subprocess.run(["explorer", audio_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", audio_path])
            else:  # Linux
                subprocess.run(["xdg-open", audio_path])
            
            logger.info(f"打开音频目录: {audio_path}")
        
        except Exception as e:
            logger.error(f"打开音频目录失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"打开音频目录失败: {str(e)}")
    
    def refresh_data(self):
        """刷新页面数据"""
        self.load_audio_files()
