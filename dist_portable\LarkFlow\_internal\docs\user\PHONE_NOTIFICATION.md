# 电话提醒功能使用说明

## 功能概述

当检测到匹配的飞书消息时，系统会并行执行：
1. 🎵 循环播放提醒音乐
2. 📞 发送飞书紧急电话提醒
3. 🔔 显示弹窗提醒
4. ⏹️ 用户点击"确定"后停止音乐

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 启用电话提醒功能
ENABLE_PHONE_NOTIFICATION=true

# 飞书应用配置
LARK_APP_ID="your_app_id_here"
LARK_APP_SECRET="your_app_secret_here"

# 电话提醒目标用户ID（多个用逗号分隔）
PHONE_NOTIFICATION_USER_IDS="user_id_1,user_id_2"

# 电话提醒消息ID列表（多个用逗号分隔）
PHONE_NOTIFICATION_MESSAGE_IDS="msg_id_1,msg_id_2,msg_id_3,msg_id_4"

# 电话提醒间隔时间（秒）
PHONE_NOTIFICATION_INTERVAL=15
```

### 2. 飞书应用配置

1. **创建飞书应用**：
   - 访问 [飞书开放平台](https://open.feishu.cn/)
   - 创建企业自建应用
   - 获取 App ID 和 App Secret

2. **配置应用权限**：
   - 添加 `im:message:urgent_phone` 权限
   - 发布应用并获得管理员审批

3. **获取用户ID**：
   - 使用飞书API获取目标用户的 union_id
   - 配置到 `PHONE_NOTIFICATION_USER_IDS`

### 3. 消息ID配置

消息ID用于电话提醒的载体，可以是：
- 历史消息ID
- 预设的消息模板ID
- 系统生成的消息ID

## 功能特点

- ✅ 并行执行音乐和电话提醒
- ✅ 支持多个用户同时提醒
- ✅ 支持多条消息间隔发送
- ✅ 自动token管理和缓存
- ✅ 完整的错误处理
- ✅ 可独立启用/禁用

## 工作流程

```
消息匹配成功
    ↓
并行启动提醒
    ├── 音乐提醒 → 播放音乐 → 显示弹窗 → 用户确认停止
    └── 电话提醒 → 获取token → 发送电话 → 间隔重复
```

## 日志信息

系统会输出以下日志：
- `成功获取飞书访问令牌`
- `电话提醒线程已启动`
- `成功发送电话提醒 - 消息ID: xxx, 用户: xxx`
- `电话提醒 1/4 发送成功`
- `等待 15 秒后发送下一个提醒...`
- `所有电话提醒发送完成`

## 故障排除

### 1. 电话提醒不发送

**可能原因**：
- 飞书应用配置错误
- 权限不足
- 用户ID或消息ID无效

**解决方案**：
```bash
# 检查配置
echo $LARK_APP_ID
echo $LARK_APP_SECRET

# 检查日志
tail -f logs/app.log
```

### 2. Token获取失败

**可能原因**：
- App ID或Secret错误
- 网络连接问题
- 应用未发布

**解决方案**：
- 验证应用配置
- 检查网络连接
- 确认应用状态

### 3. 禁用电话提醒

在 `.env` 文件中设置：
```env
ENABLE_PHONE_NOTIFICATION=false
```

## 注意事项

1. **权限要求**：需要飞书应用的电话提醒权限
2. **频率限制**：注意飞书API的调用频率限制
3. **用户体验**：合理设置提醒间隔，避免过度打扰
4. **安全性**：妥善保管App Secret，不要泄露
5. **测试建议**：先在测试环境验证配置正确性

## API参考

### 获取访问令牌
```
POST https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
```

### 发送紧急电话
```
PATCH https://open.feishu.cn/open-apis/im/v1/messages/{message_id}/urgent_phone
```

更多API详情请参考 [飞书开放平台文档](https://open.feishu.cn/document/)
