import asyncio
import threading
import time

import requests
import websockets
import static.proto_pb2 as FLY_BO<PERSON>_PROTO
from loguru import logger
from urllib.parse import urlencode
from app.api.auth import get_auth
from builder.header import HeaderBuilder
from builder.proto import ProtoBuilder
from app.config.settings import settings



class LarkClient:
    loop = None
    loop_thread = None
    """Client for interacting with Lark APIs"""

    def __init__(self, auth):
        self.auth = auth
        self.base_url = settings.LARK_BASE_URL
        self.csrf_token_url = settings.LARK_CSRF_TOKEN_URL
        self.user_info_url = settings.LARK_USER_INFO_URL
        self.ws_base_url = settings.LARK_WS_URL

        _, self.x_csrf_token = self.get_csrf_token()
        _, self.me_id = self.get_self_user_info()
        self.me_id = str(self.me_id)

    def get_csrf_token(self):
        from builder.params import ParamsBuilder
        """Get CSRF token"""
        headers = HeaderBuilder.build_get_csrf_token_header().get()
        params = ParamsBuilder.build_get_csrf_token_param().get()
        response = requests.post(self.csrf_token_url, headers=headers, cookies=self.auth.cookie, params=params)
        res_json = response.json()
        x_csrf_token = response.cookies.get('swp_csrf_token')
        if not x_csrf_token:
            logger.error("未在响应中找到swp_csrf_token")
        return res_json, x_csrf_token

    def get_self_user_info(self):
        from builder.params import ParamsBuilder
        """Get current user info"""
        headers = HeaderBuilder.build_get_user_info_header(self.x_csrf_token).get()
        params = ParamsBuilder.build_get_user_info_param().get()
        response = requests.get(self.user_info_url, headers=headers, cookies=self.auth.cookie, params=params)
        res_json = response.json()
        user_id = res_json['data']['user']['id']
        return res_json, user_id

    def search_some(self, query):
        """Search for users or groups"""
        headers = HeaderBuilder.build_search_header().get()
        Packet = ProtoBuilder.build_search_request_proto(headers['x-request-id'], query)
        response = requests.post(self.base_url, headers=headers, cookies=self.auth.cookie, data=Packet.SerializeToString())
        SearchResponsePacket, userAndGroupIds = ProtoBuilder.decode_search_response_proto(response.content)
        return SearchResponsePacket, userAndGroupIds

    def create_chat(self, userId):
        """Create a chat with a user or group"""
        headers = HeaderBuilder.build_create_chat_header().get()
        Packet = ProtoBuilder.build_create_chat_request_proto(headers['x-request-id'], userId)
        response = requests.post(self.base_url, headers=headers, cookies=self.auth.cookie, data=Packet.SerializeToString())
        PutChatResponsePacket, chatId = ProtoBuilder.decode_create_chat_response_proto(response.content)
        return PutChatResponsePacket, chatId

    def send_msg(self, sends_text, chatId):
        """Send a message to a chat"""
        headers = HeaderBuilder.build_send_msg_header().get()
        Packet = ProtoBuilder.build_send_message_request_proto(sends_text, headers['x-request-id'], chatId)
        response = requests.post(self.base_url, headers=headers, cookies=self.auth.cookie, data=Packet.SerializeToString())
        return response

    def get_other_user_all_name(self, user_id, chat_id):
        """Get another user's display name"""
        headers = HeaderBuilder.build_get_user_all_name_header().get()
        packet = ProtoBuilder.build_get_user_all_name_request_proto(headers['x-request-id'], user_id, chat_id)
        response = requests.post(self.base_url, headers=headers, cookies=self.auth.cookie, data=packet.SerializeToString())
        content = response.content
        user_name = ProtoBuilder.decode_info_response_proto(content)
        return user_name

    def get_group_name(self, chat_id):
        """Get group chat name"""
        headers = HeaderBuilder.build_get_group_name_header().get()
        packet = ProtoBuilder.build_get_group_name_request_proto(headers['x-request-id'], chat_id)
        response = requests.post(self.base_url, headers=headers, cookies=self.auth.cookie, data=packet.SerializeToString())
        group_name = ProtoBuilder.decode_group_info_response_proto(response.content)
        return group_name

    async def send_ack(self, ws, packet_sid):
        """Send acknowledgment for received messages"""
        payload = FLY_BOOK_PROTO.Packet()
        payload.cmd = 1
        payload.payloadType = 1
        payload.sid = packet_sid
        payload = payload.SerializeToString()

        frame = FLY_BOOK_PROTO.Frame()
        current = int(time.time() * 1000)
        frame.seqid = current
        frame.logid = current
        frame.service = 1
        frame.method = 1

        extended_entry = FLY_BOOK_PROTO.ExtendedEntry()
        extended_entry.key = 'x-request-time'
        extended_entry.value = f'{current}000'
        frame.headers.append(extended_entry)
        frame.payloadType = "pb"
        frame.payload = payload

        serialized_frame = frame.SerializeToString()
        await ws.send(serialized_frame)

    @staticmethod
    def start_message_processor():
        # 检查是否已有活跃的事件循环
        if LarkClient.loop is None or LarkClient.loop.is_closed():
            logger.debug("创建新的消息处理事件循环")
            LarkClient.loop = asyncio.new_event_loop()

            def run_loop(loop):
                asyncio.set_event_loop(loop)
                try:
                    logger.debug("消息处理事件循环开始运行")
                    loop.run_forever()
                except KeyboardInterrupt:
                    logger.debug("消息处理事件循环被中断")
                except Exception as e:
                    logger.error(f"消息处理事件循环异常: {e}")
                finally:
                    logger.debug("消息处理事件循环正在关闭")
                    loop.close()

            LarkClient.loop_thread = threading.Thread(target=run_loop, args=(LarkClient.loop,), daemon=True)
            LarkClient.loop_thread.start()
        else:
            logger.debug("消息处理事件循环已存在，跳过创建")

    @staticmethod
    def stop_message_processor():
        if LarkClient.loop is not None and not LarkClient.loop.is_closed():
            try:
                LarkClient.loop.call_soon_threadsafe(LarkClient.loop.stop)
                logger.debug("事件循环停止信号已发送")
            except RuntimeError as e:
                if "Event loop is closed" not in str(e):
                    logger.warning(f"停止事件循环时出错: {e}")

        # 重置循环引用
        LarkClient.loop = None
        LarkClient.loop_thread = None

    async def connect_websocket(self, message_handler):
        """Connect to Lark websocket with heartbeat - single connection attempt"""
        from builder.params import ParamsBuilder

        websocket = None
        heartbeat_task = None
        try:
            # 构建连接参数
            params = ParamsBuilder.build_receive_msg_param(self.auth).get()
            url = f"{self.ws_base_url}?{urlencode(params)}"

            logger.info("正在连接WebSocket...")
            websocket = await websockets.connect(url)
            logger.info("WebSocket连接建立成功")

            # 连接成功后立即设置状态
            try:
                from app.core.state_manager import state_manager
                state_manager.set_websocket_connected(True)
                state_manager.reset_reconnect_count()
                logger.info("WebSocket连接状态已更新为已连接")
            except Exception as e:
                logger.debug(f"更新连接状态失败: {str(e)}")

            # 连接成功后才启动消息处理器
            LarkClient.start_message_processor()

            # 启动心跳任务
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(websocket))

            # 主消息循环
            await self._message_loop(websocket, message_handler)

            # 如果到达这里，说明消息循环正常结束
            logger.info("消息接收循环正常结束")

        except asyncio.CancelledError:
            logger.info("WebSocket连接被取消")
            raise
        except KeyboardInterrupt:
            logger.info("WebSocket连接被用户中断")
            raise
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket连接被服务器关闭")
            raise
        except Exception as e:
            logger.error(f"WebSocket连接失败: {str(e)}")
            raise
        finally:
            # 取消心跳任务
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
                try:
                    await heartbeat_task
                except asyncio.CancelledError:
                    pass

            # 关闭WebSocket连接
            if websocket:
                try:
                    await websocket.close()
                    logger.debug("WebSocket连接已关闭")
                except Exception as e:
                    logger.debug(f"关闭WebSocket时出错: {e}")

            # 确保事件循环正确停止
            try:
                LarkClient.stop_message_processor()
            except Exception as e:
                logger.debug(f"停止消息处理器时出错: {e}")

    async def _heartbeat_loop(self, websocket):
        """心跳循环，定期发送心跳包保持连接活跃"""
        heartbeat_interval = 300  # 5分钟发送一次心跳

        try:
            while True:
                await asyncio.sleep(heartbeat_interval)

                try:
                    # 发送ping帧作为心跳
                    await websocket.ping()
                    logger.debug("发送心跳包")

                    # 更新状态管理器的心跳时间
                    try:
                        from app.core.state_manager import state_manager
                        state_manager.update_heartbeat()
                    except Exception as e:
                        logger.debug(f"更新心跳状态失败: {str(e)}")

                except websockets.exceptions.ConnectionClosed:
                    logger.warning("心跳发送失败：连接已关闭")
                    break
                except Exception as e:
                    logger.error(f"心跳发送失败: {str(e)}")
                    break

        except asyncio.CancelledError:
            logger.debug("心跳循环被取消")
            raise

    async def _message_loop(self, websocket, message_handler):
        """主消息接收循环"""
        last_message_time = time.time()
        connection_timeout = 1800  # 30分钟无消息则认为连接可能有问题

        while True:
            try:
                # 使用较长的超时时间，但定期检查连接状态
                message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                last_message_time = time.time()

                try:
                    packet_sid = ProtoBuilder.extra_packet_id(message)
                    await self.send_ack(websocket, packet_sid)
                    ReceiveTextContent = ProtoBuilder.decode_receive_msg_proto(message)

                    # 只有当解析成功且包含有效内容时才处理消息
                    if ReceiveTextContent and ReceiveTextContent.get('fromId'):
                        # 检查事件循环状态，避免在关闭的循环中运行协程
                        if LarkClient.loop and not LarkClient.loop.is_closed():
                            try:
                                future = asyncio.run_coroutine_threadsafe(
                                    self.process_msg(ReceiveTextContent, message_handler),
                                    LarkClient.loop
                                )
                                # 不等待结果，避免阻塞WebSocket接收
                                logger.debug("消息处理任务已提交")
                            except RuntimeError as re:
                                if "Event loop is closed" in str(re):
                                    logger.warning("事件循环已关闭，跳过消息处理")
                                else:
                                    logger.error(f"消息处理运行时错误: {re}")
                            except Exception as e:
                                logger.error(f"提交消息处理任务失败: {e}")
                        else:
                            logger.warning("事件循环不可用，跳过消息处理")

                except Exception as e:
                    logger.error(f"处理消息时出错: {e}")
                    logger.debug(f"消息长度: {len(message) if message else 0}")
                    continue

            except asyncio.TimeoutError:
                # 检查是否长时间无消息
                current_time = time.time()
                if current_time - last_message_time > connection_timeout:
                    logger.warning(f"超过 {connection_timeout/60} 分钟无消息，连接可能异常")
                    # 发送ping检查连接状态
                    try:
                        pong_waiter = await websocket.ping()
                        await asyncio.wait_for(pong_waiter, timeout=10)
                        logger.info("连接状态检查正常")
                        last_message_time = current_time  # 重置计时器
                    except Exception as e:
                        logger.error(f"连接状态检查失败: {str(e)}")
                        raise websockets.exceptions.ConnectionClosed(None, None)
                continue

            except websockets.exceptions.ConnectionClosed:
                logger.info("WebSocket连接已关闭")
                raise

    async def process_msg(self, msg, message_handler):
        from_id, chat_id, chat_type, content = msg['fromId'], msg['chatId'], msg['chatType'], msg['content']
        user_name = self.get_other_user_all_name(from_id, chat_id)
        is_group_chat = (chat_type == 2)
        group_name = None
        if is_group_chat:
            group_name = self.get_group_name(chat_id)
        await message_handler(
            user_name=user_name,
            user_id=from_id,
            content=content,
            is_group_chat=is_group_chat,
            group_name=group_name,
            chat_id=chat_id
        )


if __name__ == '__main__':
    auth = get_auth()
    lark_client = LarkClient(auth)
    fromId = 7478340774602522627
    chatId = 7478340637890854916
    chatId = 7373962691750363140
    user_name = lark_client.get_other_user_all_name(fromId, chatId)
    print(user_name)

    group_name = lark_client.get_group_name(chatId)
    print(group_name)
