"""
网络连接检查工具
检查网络连接状态和DNS解析
"""
import socket
import requests
from loguru import logger
import time

class NetworkChecker:
    """网络连接检查器"""
    
    def __init__(self):
        self.timeout = 10
        self.test_urls = [
            'https://www.baidu.com',
            'https://www.google.com',
            'https://internal-api-lark-api.feishu.cn'
        ]
        self.dns_servers = [
            '8.8.8.8',  # Google DNS
            '114.114.114.114',  # 114 DNS
            '223.5.5.5'  # 阿里DNS
        ]
    
    def check_internet_connection(self):
        """检查基本网络连接"""
        try:
            # 尝试连接到常用网站
            for url in self.test_urls[:2]:  # 只测试百度和谷歌
                try:
                    response = requests.get(url, timeout=self.timeout)
                    if response.status_code == 200:
                        logger.info(f"网络连接正常: {url}")
                        return True
                except:
                    continue
            
            logger.warning("无法连接到测试网站")
            return False
            
        except Exception as e:
            logger.error(f"网络连接检查失败: {e}")
            return False
    
    def check_dns_resolution(self, hostname):
        """检查DNS解析"""
        try:
            ip = socket.gethostbyname(hostname)
            logger.info(f"DNS解析成功: {hostname} -> {ip}")
            return True, ip
        except socket.gaierror as e:
            logger.error(f"DNS解析失败: {hostname} - {e}")
            return False, None
    
    def check_feishu_connectivity(self):
        """检查飞书服务连接"""
        hostname = 'internal-api-lark-api.feishu.cn'
        
        # 检查DNS解析
        dns_ok, ip = self.check_dns_resolution(hostname)
        if not dns_ok:
            return False, "DNS解析失败"
        
        # 检查端口连接
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((hostname, 443))
            sock.close()
            
            if result == 0:
                logger.info(f"飞书服务连接正常: {hostname}:443")
                return True, "连接正常"
            else:
                logger.warning(f"飞书服务端口不可达: {hostname}:443")
                return False, "端口不可达"
                
        except Exception as e:
            logger.error(f"飞书服务连接检查失败: {e}")
            return False, f"连接检查失败: {e}"
    
    def get_network_diagnosis(self):
        """获取网络诊断信息"""
        diagnosis = {
            'internet_ok': False,
            'feishu_ok': False,
            'dns_ok': False,
            'suggestions': []
        }
        
        # 检查基本网络连接
        diagnosis['internet_ok'] = self.check_internet_connection()
        if not diagnosis['internet_ok']:
            diagnosis['suggestions'].append("请检查网络连接")
        
        # 检查飞书连接
        feishu_ok, feishu_msg = self.check_feishu_connectivity()
        diagnosis['feishu_ok'] = feishu_ok
        
        if not feishu_ok:
            if "DNS解析失败" in feishu_msg:
                diagnosis['dns_ok'] = False
                diagnosis['suggestions'].extend([
                    "DNS解析失败，请尝试：",
                    "1. 更换DNS服务器（8.8.8.8 或 114.114.114.114）",
                    "2. 检查网络代理设置",
                    "3. 联系网络管理员"
                ])
            elif "端口不可达" in feishu_msg:
                diagnosis['suggestions'].extend([
                    "飞书服务端口被阻止，请尝试：",
                    "1. 检查防火墙设置",
                    "2. 检查企业网络策略",
                    "3. 尝试使用VPN"
                ])
            else:
                diagnosis['suggestions'].append(f"飞书连接问题: {feishu_msg}")
        
        return diagnosis
    
    def wait_for_network(self, max_wait=60):
        """等待网络连接恢复"""
        logger.info("等待网络连接恢复...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            if self.check_internet_connection():
                feishu_ok, _ = self.check_feishu_connectivity()
                if feishu_ok:
                    logger.info("网络连接已恢复")
                    return True
            
            time.sleep(5)
        
        logger.warning("网络连接等待超时")
        return False

# 全局网络检查器实例
network_checker = NetworkChecker()
