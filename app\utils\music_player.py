"""
音乐播放器模块
用于播放提醒音乐 - 本地播放模式
"""
import os
import threading
import time
from pathlib import Path
from loguru import logger

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    logger.warning("pygame未安装，音乐播放功能将不可用")

class MusicPlayer:
    """音乐播放器类 - 本地播放模式"""

    def __init__(self):
        self.is_playing = False
        self.should_stop = False
        self.play_thread = None
        self.current_music_file = None
        self.mixer_initialized = False

        if PYGAME_AVAILABLE:
            self._init_pygame()
            logger.info("本地音乐播放器初始化成功")
        else:
            logger.error("pygame不可用，音乐播放功能已禁用")

    def _init_pygame(self):
        """初始化pygame mixer"""
        try:
            pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
            pygame.mixer.init()
            self.mixer_initialized = True
            logger.debug("pygame mixer初始化成功")
        except Exception as e:
            logger.error(f"pygame mixer初始化失败: {str(e)}")
            self.mixer_initialized = False

    def _resolve_music_file_path(self, music_file):
        """解析音乐文件路径，支持相对路径和绝对路径"""
        if os.path.isabs(music_file):
            return music_file

        # 相对路径，基于项目根目录解析
        project_root = Path(__file__).parent.parent.parent
        full_path = project_root / music_file
        return str(full_path)

    def play_loop(self, music_file):
        """本地播放音乐 - 使用pygame循环播放"""
        if not PYGAME_AVAILABLE or not self.mixer_initialized:
            logger.error("pygame不可用，无法播放音乐")
            return

        # 解析文件路径
        resolved_path = self._resolve_music_file_path(music_file)

        if not os.path.exists(resolved_path):
            logger.error(f"音乐文件不存在: {resolved_path}")
            return

        try:
            self.is_playing = True
            self.should_stop = False
            self.current_music_file = resolved_path

            logger.info(f"开始本地音乐播放: {resolved_path}")

            # 加载并播放音乐
            pygame.mixer.music.load(resolved_path)
            pygame.mixer.music.play(-1)  # -1表示无限循环

            # 等待播放完成或被停止
            while not self.should_stop and pygame.mixer.music.get_busy():
                time.sleep(0.1)

        except Exception as e:
            logger.error(f"本地音乐播放器出错: {str(e)}")
        finally:
            try:
                pygame.mixer.music.stop()
            except:
                pass
            self.is_playing = False
            self.current_music_file = None
            logger.info("本地音乐播放已停止")



    def start_playing(self, music_file):
        """开始播放音乐（在新线程中）- 支持重复触发"""
        # 如果正在播放，先停止当前播放
        if self.is_playing:
            logger.info("停止当前播放，启动新的音乐播放")
            self.stop_playing()
            # 等待当前播放线程结束
            if self.play_thread and self.play_thread.is_alive():
                self.play_thread.join(timeout=1)

        # 启动新的播放线程
        self.play_thread = threading.Thread(
            target=self.play_loop,
            args=(music_file,),
            daemon=True
        )
        self.play_thread.start()
        logger.info(f"新的音乐播放线程已启动: {music_file}")

    def stop_playing(self):
        """停止播放音乐"""
        if not self.is_playing:
            return

        logger.info("正在停止本地音乐播放...")
        self.should_stop = True

        # 停止pygame音乐播放
        if PYGAME_AVAILABLE and self.mixer_initialized:
            try:
                pygame.mixer.music.stop()
            except Exception as e:
                logger.debug(f"停止pygame音乐时出错: {str(e)}")

        # 等待播放线程结束
        if self.play_thread and self.play_thread.is_alive():
            self.play_thread.join(timeout=2)

    def is_music_playing(self):
        """检查音乐是否正在播放"""
        return self.is_playing

# 全局音乐播放器实例
music_player = MusicPlayer()
