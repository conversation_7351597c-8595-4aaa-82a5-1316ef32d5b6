"""
应用服务管理器
负责管理LarkAgentX主应用的启动、停止和状态监控
"""
import asyncio
import threading
from typing import Optional, Callable
from loguru import logger

from app.utils.resource_manager import global_resource_manager


class AppServiceManager:
    """应用服务管理器"""
    
    def __init__(self):
        self.is_running = False
        self.main_task: Optional[asyncio.Task] = None
        self.event_loop: Optional[asyncio.AbstractEventLoop] = None
        self.app_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.error_callback: Optional[Callable[[str], None]] = None

        # 资源管理器
        self.resource_manager = global_resource_manager
        
    def set_error_callback(self, callback: Callable[[str], None]):
        """设置错误回调函数"""
        self.error_callback = callback
        
    def start_application(self) -> bool:
        """启动主应用"""
        if self.is_running:
            logger.info("应用已在运行中，无需重复启动")
            return True  # 返回True表示应用正在运行

        try:
            logger.info("正在启动LarkAgentX主应用...")
            self.stop_event.clear()

            # 在新线程中运行主应用
            self.app_thread = threading.Thread(target=self._run_main_app, daemon=True)
            self.app_thread.start()

            # 注册线程资源
            self.resource_manager.thread_manager.register_thread(self.app_thread)

            self.is_running = True
            logger.info("LarkAgentX主应用启动成功")
            return True
            
        except Exception as e:
            error_msg = f"启动应用失败: {str(e)}"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
    
    def stop_application(self) -> bool:
        """停止主应用"""
        if not self.is_running:
            logger.warning("应用未在运行")
            return False
            
        try:
            logger.info("正在停止LarkAgentX主应用...")
            
            # 设置停止事件
            self.stop_event.set()
            
            # 取消主任务
            if self.main_task and not self.main_task.done():
                self.main_task.cancel()
            
            # 等待线程结束
            if self.app_thread and self.app_thread.is_alive():
                self.app_thread.join(timeout=5)

            # 清理所有资源
            self.resource_manager.cleanup_all()

            self.is_running = False
            self.main_task = None
            self.event_loop = None
            self.app_thread = None
            
            logger.info("LarkAgentX主应用已停止")
            return True
            
        except Exception as e:
            error_msg = f"停止应用失败: {str(e)}"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
    
    def _run_main_app(self):
        """在新线程中运行主应用"""
        try:
            # 不重新配置日志，使用主线程的日志配置
            # 这样可以确保子线程的日志也输出到控制台

            # 创建新的事件循环
            self.event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.event_loop)

            # 运行主应用逻辑
            self.main_task = self.event_loop.create_task(self._main_app_logic())
            self.event_loop.run_until_complete(self.main_task)

        except asyncio.CancelledError:
            logger.info("主应用被取消")
        except Exception as e:
            error_msg = f"主应用运行异常: {str(e)}"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
        finally:
            # 确保清理状态
            self._cleanup_state()
            logger.info("主应用线程已退出")

    def _cleanup_state(self):
        """清理应用状态"""
        try:
            if self.event_loop and not self.event_loop.is_closed():
                self.event_loop.close()

            self.is_running = False
            self.main_task = None
            self.event_loop = None

            # 通知状态管理器应用已停止
            if self.error_callback:
                # 使用错误回调来通知状态变化
                from app.core.state_manager import state_manager
                state_manager.set_state("main_app_running", False)

        except Exception as e:
            logger.error(f"清理状态时出错: {str(e)}")
    
    async def _main_app_logic(self):
        """主应用逻辑 - 从原main.py移植"""
        from app.api.auth import get_auth
        from app.api.lark_client import LarkClient
        from app.core.message_service import MessageService
        from app.core.state_manager import state_manager
        
        try:
            # 设置应用启动状态
            state_manager.start_application()

            # 同步配置文件的开关状态
            state_manager.sync_config_switches()

            logger.info("初始化认证...")
            try:
                auth = get_auth()
                logger.info("认证初始化成功.")
            except Exception as e:
                error_msg = f"认证初始化失败: {str(e)}"
                # 检查是否是Cookie相关的错误
                if "cookie" in str(e).lower() or "unauthorized" in str(e).lower() or "401" in str(e):
                    error_msg = "认证失败，可能是Cookie已过期，请在配置页面更新飞书Cookie"
                elif "expecting value" in str(e).lower():
                    error_msg = "认证失败，服务器返回无效响应，请检查Cookie是否正确或已过期"
                logger.error(error_msg)
                state_manager.set_error(error_msg)
                raise  # 重新抛出异常，让外层处理

            # 网络连接检查
            logger.info("检查网络连接...")
            from app.utils.network_check import network_checker

            diagnosis = network_checker.get_network_diagnosis()
            if not diagnosis['internet_ok']:
                error_msg = "网络连接失败，请检查网络设置"
                logger.error(error_msg)
                for suggestion in diagnosis['suggestions']:
                    logger.info(f"建议: {suggestion}")
                state_manager.set_error(error_msg)
                raise ConnectionError(error_msg)

            if not diagnosis['feishu_ok']:
                error_msg = "无法连接到飞书服务"
                logger.error(error_msg)
                for suggestion in diagnosis['suggestions']:
                    logger.info(f"建议: {suggestion}")

                # 尝试等待网络恢复
                logger.info("尝试等待网络恢复...")
                if not network_checker.wait_for_network(30):
                    state_manager.set_error(error_msg)
                    raise ConnectionError(error_msg)

            logger.info("创建 Lark 客户端...")
            try:
                lark_client = LarkClient(auth)
            except Exception as e:
                error_msg = f"创建Lark客户端失败: {str(e)}"
                logger.error(error_msg)

                # 如果是网络相关错误，提供更友好的提示
                if "getaddrinfo failed" in str(e) or "NameResolutionError" in str(e):
                    error_msg = "网络连接失败，无法解析飞书服务域名。请检查：\n1. 网络连接是否正常\n2. DNS设置是否正确\n3. 防火墙是否阻止连接"
                elif "Max retries exceeded" in str(e):
                    error_msg = "连接飞书服务超时，请检查网络连接或稍后重试"

                state_manager.set_error(error_msg)
                raise  # 重新抛出异常，让外层处理

            logger.info("创建消息服务...")
            message_service = MessageService(lark_client)
            
            logger.info("连接到 Lark WebSocket...")
            logger.info("开始接收消息...")
            logger.info('================================================================')

            # 创建WebSocket连接任务
            try:
                # 设置初始连接状态
                state_manager.set_websocket_connected(False)

                # 启动WebSocket连接（带重连机制）
                websocket_task = asyncio.create_task(
                    self._websocket_connection_manager(lark_client, message_service)
                )

                # 等待停止信号或WebSocket任务完成
                while not self.stop_event.is_set():
                    if websocket_task.done():
                        # WebSocket任务结束，检查是否有异常
                        try:
                            await websocket_task
                            logger.info("WebSocket连接管理器正常结束")
                        except Exception as e:
                            error_msg = self._analyze_websocket_error(e)
                            logger.error(error_msg)
                            state_manager.set_error(error_msg)
                            if self.error_callback:
                                self.error_callback(error_msg)
                        break

                    # 短暂等待，避免CPU占用过高
                    await asyncio.sleep(0.1)

                # 取消WebSocket任务
                if not websocket_task.done():
                    logger.info("正在取消WebSocket连接...")
                    websocket_task.cancel()
                    try:
                        await websocket_task
                    except asyncio.CancelledError:
                        logger.info("WebSocket连接已取消")

            except Exception as e:
                # 分析WebSocket连接创建错误
                error_str = str(e).lower()
                if "401" in error_str or "unauthorized" in error_str:
                    error_msg = "WebSocket连接创建失败：认证失败，Cookie可能已过期，请在配置页面更新飞书Cookie"
                elif "403" in error_str or "forbidden" in error_str:
                    error_msg = "WebSocket连接创建失败：访问被拒绝，请检查Cookie权限"
                elif "expecting value" in error_str:
                    error_msg = "WebSocket连接创建失败：服务器返回无效响应，Cookie可能已过期或无效"
                elif "connection" in error_str:
                    error_msg = "WebSocket连接创建失败：网络连接问题，请检查网络或Cookie是否有效"
                else:
                    error_msg = f"WebSocket连接创建失败: {str(e)}"

                logger.error(error_msg)
                state_manager.set_error(error_msg)
                if self.error_callback:
                    self.error_callback(error_msg)
                raise  # 重新抛出异常
                    
        except Exception as e:
            error_msg = f"主应用逻辑异常: {str(e)}"
            logger.error(error_msg)
            state_manager.set_error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
        finally:
            logger.info("主应用逻辑正在退出...")
            state_manager.set_websocket_connected(False)
            state_manager.stop_application()

    async def _websocket_connection_manager(self, lark_client, message_service):
        """WebSocket连接管理器，处理连接和重连逻辑"""
        from app.core.state_manager import state_manager

        max_reconnect_attempts = 10
        reconnect_count = 0
        base_delay = 5  # 基础重连延迟（秒）

        # 初始状态设置为未连接
        state_manager.set_websocket_connected(False)

        while not self.stop_event.is_set() and reconnect_count < max_reconnect_attempts:
            try:
                logger.info("启动WebSocket连接...")

                # 尝试连接WebSocket（这个方法会阻塞直到连接断开）
                await lark_client.connect_websocket(message_service.process_message)

                # 如果到达这里，说明连接正常结束（被服务器关闭或其他正常原因）
                logger.info("WebSocket连接正常结束")
                # 连接正常结束，不需要重连，直接退出
                break

            except asyncio.CancelledError:
                logger.info("WebSocket连接管理器被取消")
                state_manager.set_websocket_connected(False)
                raise
            except Exception as e:
                reconnect_count += 1
                state_manager.set_websocket_connected(False)
                state_manager.increment_reconnect_count()

                error_msg = self._analyze_websocket_error(e)
                logger.error(f"WebSocket连接失败 (尝试 {reconnect_count}/{max_reconnect_attempts}): {error_msg}")

                if reconnect_count >= max_reconnect_attempts:
                    logger.error("已达到最大重连次数，停止重连")
                    state_manager.set_error(f"WebSocket连接失败，已达到最大重连次数: {error_msg}")
                    break

                # 计算重连延迟（指数退避，最大120秒）
                delay = min(base_delay * (2 ** (reconnect_count - 1)), 120)
                logger.info(f"将在 {delay} 秒后尝试重连...")

                # 等待重连延迟，同时检查停止信号
                for _ in range(int(delay)):
                    if self.stop_event.is_set():
                        logger.info("收到停止信号，取消重连")
                        state_manager.set_websocket_connected(False)
                        return
                    await asyncio.sleep(1)

        # 只有在重连失败或被取消时才设置为未连接
        if reconnect_count >= max_reconnect_attempts or self.stop_event.is_set():
            state_manager.set_websocket_connected(False)

    def _analyze_websocket_error(self, error):
        """分析WebSocket连接错误类型"""
        error_str = str(error).lower()

        if "401" in error_str or "unauthorized" in error_str:
            return "WebSocket连接失败：认证失败，Cookie可能已过期，请更新飞书Cookie"
        elif "403" in error_str or "forbidden" in error_str:
            return "WebSocket连接失败：访问被拒绝，请检查Cookie权限或联系管理员"
        elif "timeout" in error_str or "timed out" in error_str:
            return "WebSocket连接失败：连接超时，请检查网络连接"
        elif "connection" in error_str and "refused" in error_str:
            return "WebSocket连接失败：连接被拒绝，请检查网络或服务器状态"
        elif "expecting value" in error_str:
            return "WebSocket连接失败：服务器返回无效响应，Cookie可能已过期"
        elif "connection closed" in error_str:
            return "WebSocket连接被服务器关闭，正在尝试重连"
        else:
            return f"WebSocket连接异常: {str(error)}"

    def get_status(self) -> dict:
        """获取应用状态"""
        return {
            "is_running": self.is_running,
            "has_thread": self.app_thread is not None and self.app_thread.is_alive(),
            "has_task": self.main_task is not None and not self.main_task.done(),
            "has_loop": self.event_loop is not None and not self.event_loop.is_closed()
        }


# 全局应用服务管理器实例
app_service_manager = AppServiceManager()
