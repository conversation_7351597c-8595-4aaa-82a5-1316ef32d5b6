"""
状态概览页面
显示系统运行状态、统计信息和功能开关控制
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QCheckBox, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont
from loguru import logger

from app.core.state_manager import state_manager


class StatusPage(QWidget):
    """状态概览页面"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.connect_signals()
        self.setup_event_listeners()

        # 初始化时检查应用运行状态
        self.update_app_running_status()

        logger.info("状态概览页面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title_label = QLabel("状态概览")
        title_label.setObjectName("title")
        layout.addWidget(title_label)
        
        # 创建状态卡片区域
        self.create_status_cards(layout)
        
        # 创建控制面板
        self.create_control_panel(layout)
        
        # 创建统计信息
        self.create_statistics(layout)
        
        layout.addStretch()
    
    def create_status_cards(self, parent_layout):
        """创建状态卡片"""
        cards_layout = QHBoxLayout()
        
        # 系统状态卡片
        self.system_card = QFrame()
        self.system_card.setObjectName("card")
        self.system_card.setMinimumHeight(140)
        self.system_card.setMaximumHeight(160)

        system_layout = QVBoxLayout(self.system_card)
        system_layout.setContentsMargins(15, 10, 15, 10)
        system_layout.setSpacing(8)

        # 卡片标题
        title_layout = QHBoxLayout()
        icon_label = QLabel("🖥️")
        icon_label.setStyleSheet("font-size: 16px;")
        title_label = QLabel("系统状态")
        title_label.setStyleSheet("font-weight: 600; font-size: 14px; color: #333333;")

        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        system_layout.addLayout(title_layout)

        # 状态信息
        self.system_status_label = QLabel("检查中...")
        self.system_status_label.setObjectName("status-running")
        self.system_status_label.setStyleSheet("font-size: 13px; margin: 5px 0;")

        self.system_uptime_label = QLabel("运行时间: --")
        self.system_uptime_label.setStyleSheet("font-size: 12px; color: #666666;")

        system_layout.addWidget(self.system_status_label)
        system_layout.addWidget(self.system_uptime_label)
        system_layout.addStretch()

        # 连接状态卡片
        self.connection_card = QFrame()
        self.connection_card.setObjectName("card")
        self.connection_card.setMinimumHeight(140)
        self.connection_card.setMaximumHeight(160)

        connection_layout = QVBoxLayout(self.connection_card)
        connection_layout.setContentsMargins(15, 10, 15, 10)
        connection_layout.setSpacing(8)

        # 卡片标题
        title_layout = QHBoxLayout()
        icon_label = QLabel("🔗")
        icon_label.setStyleSheet("font-size: 16px;")
        title_label = QLabel("连接状态")
        title_label.setStyleSheet("font-weight: 600; font-size: 14px; color: #495057;")

        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        connection_layout.addLayout(title_layout)

        self.websocket_status_label = QLabel("未连接")
        self.websocket_status_label.setStyleSheet("font-size: 13px; margin: 5px 0;")

        self.connection_quality_label = QLabel("连接质量: --")
        self.connection_quality_label.setStyleSheet("font-size: 12px; color: #6c757d;")

        connection_layout.addWidget(self.websocket_status_label)
        connection_layout.addWidget(self.connection_quality_label)
        connection_layout.addStretch()
        
        # 主应用状态卡片
        self.app_card = QFrame()
        self.app_card.setObjectName("card")
        self.app_card.setMinimumHeight(140)
        self.app_card.setMaximumHeight(160)

        app_layout = QVBoxLayout(self.app_card)
        app_layout.setContentsMargins(15, 10, 15, 10)
        app_layout.setSpacing(8)

        # 卡片标题
        title_layout = QHBoxLayout()
        icon_label = QLabel("🚀")
        icon_label.setStyleSheet("font-size: 16px;")
        title_label = QLabel("主应用")
        title_label.setStyleSheet("font-weight: 600; font-size: 14px; color: #495057;")

        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        app_layout.addLayout(title_layout)

        # 状态标签
        self.app_status_label = QLabel("未启动")
        self.app_status_label.setStyleSheet("font-size: 13px; margin: 5px 0;")
        app_layout.addWidget(self.app_status_label)

        # 控制按钮
        self.app_control_layout = QHBoxLayout()
        self.app_control_layout.setSpacing(10)

        self.start_button = QPushButton("启动应用")
        self.start_button.setObjectName("success")
        self.start_button.setMinimumHeight(32)
        self.start_button.clicked.connect(self.start_main_app)

        self.stop_button = QPushButton("停止应用")
        self.stop_button.setObjectName("danger")
        self.stop_button.setMinimumHeight(32)
        self.stop_button.clicked.connect(self.stop_main_app)
        self.stop_button.setEnabled(False)

        self.app_control_layout.addWidget(self.start_button)
        self.app_control_layout.addWidget(self.stop_button)

        app_layout.addLayout(self.app_control_layout)
        
        cards_layout.addWidget(self.system_card)
        cards_layout.addWidget(self.connection_card)
        cards_layout.addWidget(self.app_card)
        
        parent_layout.addLayout(cards_layout)
    
    def create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_group = QGroupBox("功能控制")
        control_layout = QGridLayout(control_group)
        
        # 功能开关
        self.message_listening_cb = QCheckBox("消息监听")
        self.auto_reply_cb = QCheckBox("自动回复")
        self.music_notification_cb = QCheckBox("音乐提醒")
        self.phone_notification_cb = QCheckBox("电话提醒")
        
        # 连接开关事件
        self.message_listening_cb.toggled.connect(
            lambda checked: self.toggle_switch('message_listening', checked))
        self.auto_reply_cb.toggled.connect(
            lambda checked: self.toggle_switch('auto_reply', checked))
        self.music_notification_cb.toggled.connect(
            lambda checked: self.toggle_switch('music_notification', checked))
        self.phone_notification_cb.toggled.connect(
            lambda checked: self.toggle_switch('phone_notification', checked))
        
        control_layout.addWidget(self.message_listening_cb, 0, 0)
        control_layout.addWidget(self.auto_reply_cb, 0, 1)
        control_layout.addWidget(self.music_notification_cb, 1, 0)
        control_layout.addWidget(self.phone_notification_cb, 1, 1)
        
        parent_layout.addWidget(control_group)
    
    def create_statistics(self, parent_layout):
        """创建统计信息"""
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout(stats_group)
        
        # 统计标签
        self.messages_count_label = QLabel("0")
        self.messages_count_label.setObjectName("metric-number")
        self.replies_count_label = QLabel("0")
        self.replies_count_label.setObjectName("metric-number")
        self.music_count_label = QLabel("0")
        self.music_count_label.setObjectName("metric-number")
        self.phone_count_label = QLabel("0")
        self.phone_count_label.setObjectName("metric-number")
        
        # 添加到布局
        stats_layout.addWidget(QLabel("接收消息"), 0, 0)
        stats_layout.addWidget(self.messages_count_label, 1, 0)
        stats_layout.addWidget(QLabel("发送回复"), 0, 1)
        stats_layout.addWidget(self.replies_count_label, 1, 1)
        stats_layout.addWidget(QLabel("音乐提醒"), 0, 2)
        stats_layout.addWidget(self.music_count_label, 1, 2)
        stats_layout.addWidget(QLabel("电话提醒"), 0, 3)
        stats_layout.addWidget(self.phone_count_label, 1, 3)
        
        parent_layout.addWidget(stats_group)
    
    def create_card(self, title, icon):
        """创建状态卡片"""
        card = QFrame()
        card.setObjectName("card")
        card.setFixedHeight(120)
        
        layout = QVBoxLayout(card)
        
        # 卡片标题
        title_layout = QHBoxLayout()
        title_layout.addWidget(QLabel(icon))
        title_layout.addWidget(QLabel(title))
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        layout.addStretch()
        
        return card
    
    def connect_signals(self):
        """连接信号槽"""
        # 连接按钮信号
        if hasattr(self, 'start_button'):
            self.start_button.clicked.connect(self.start_main_app)
        if hasattr(self, 'stop_button'):
            self.stop_button.clicked.connect(self.stop_main_app)

    def setup_event_listeners(self):
        """设置事件监听器"""
        try:
            from app.utils.event_emitter import state_event_emitter

            # 监听状态变化事件
            state_event_emitter.on(state_event_emitter.STATE_CHANGED, self.on_state_changed)
            state_event_emitter.on(state_event_emitter.WEBSOCKET_CONNECTED, self.on_websocket_event)
            state_event_emitter.on(state_event_emitter.WEBSOCKET_DISCONNECTED, self.on_websocket_event)
            state_event_emitter.on(state_event_emitter.ERROR_OCCURRED, self.on_error_event)
            state_event_emitter.on(state_event_emitter.CONNECTION_QUALITY_CHANGED, self.on_connection_quality_changed)

            logger.info("状态页面事件监听器设置完成")
        except ImportError:
            logger.warning("事件发射器不可用，使用定时器更新")
            # 如果事件发射器不可用，使用定时器作为后备方案
            self.setup_timer_fallback()

    def setup_timer_fallback(self):
        """设置定时器后备方案"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_data)
        self.timer.start(3000)  # 3秒更新一次
    
    def toggle_switch(self, switch_name, enabled):
        """切换功能开关"""
        try:
            switch_mapping = {
                'message_listening': 'message_listening_enabled',
                'auto_reply': 'auto_reply_enabled',
                'music_notification': 'music_notification_enabled',
                'phone_notification': 'phone_notification_enabled'
            }
            
            if switch_name in switch_mapping:
                state_key = switch_mapping[switch_name]
                state_manager.set_state(state_key, enabled)
                logger.info(f"开关状态变更: {switch_name} = {enabled}")
            
        except Exception as e:
            logger.error(f"切换开关失败: {str(e)}")
            self.show_notification(f"切换开关失败: {str(e)}", "error")
    
    def start_main_app(self):
        """启动主应用"""
        try:
            # 检查应用是否已经在运行
            if state_manager.is_main_application_running():
                logger.info("主应用已在运行中")
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.app_status_label.setText("运行中")
                self.show_notification("应用已在运行中", "info")
                return

            success = state_manager.start_main_application()
            if success:
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.app_status_label.setText("启动中...")
                self.show_notification("主应用启动成功", "success")
                logger.info("主应用启动请求已发送")
            else:
                logger.error("主应用启动失败")
                self.show_notification("主应用启动失败", "error")
        except Exception as e:
            logger.error(f"启动主应用失败: {str(e)}")
            self.show_notification(f"启动失败: {str(e)}", "error")
    
    def stop_main_app(self):
        """停止主应用"""
        try:
            success = state_manager.stop_main_application()
            if success:
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                self.app_status_label.setText("停止中...")
                self.show_notification("主应用停止成功", "success")
                logger.info("主应用停止请求已发送")
            else:
                self.show_notification("主应用停止失败", "error")
        except Exception as e:
            logger.error(f"停止主应用失败: {str(e)}")
            self.show_notification(f"停止失败: {str(e)}", "error")
    
    def update_status(self, status):
        """更新状态显示"""
        try:
            # 更新系统状态
            if status.get('running', False):
                self.system_status_label.setText("运行中")
                self.system_status_label.setObjectName("status-running")
            else:
                self.system_status_label.setText("已停止")
                self.system_status_label.setObjectName("status-stopped")
            
            # 更新运行时间
            uptime = status.get('uptime_formatted', '--')
            self.system_uptime_label.setText(f"运行时间: {uptime}")
            
            # 更新连接状态
            if status.get('websocket_connected', False):
                self.websocket_status_label.setText("已连接")
                self.websocket_status_label.setObjectName("status-running")
            else:
                self.websocket_status_label.setText("未连接")
                self.websocket_status_label.setObjectName("status-stopped")

            # 更新连接质量
            connection_quality = status.get('connection_quality', 'unknown')
            quality_text = self._format_connection_quality(connection_quality)
            self.connection_quality_label.setText(f"连接质量: {quality_text}")

            # 根据连接质量设置颜色
            if connection_quality == 'good':
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #28a745;")
            elif connection_quality == 'poor':
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #ffc107;")
            elif connection_quality == 'disconnected':
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #dc3545;")
            else:
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #6c757d;")
            
            # 更新主应用状态
            main_app_running = status.get('main_app_running', False)
            if main_app_running:
                self.app_status_label.setText("运行中")
                self.app_status_label.setObjectName("status-running")
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
            else:
                self.app_status_label.setText("未启动")
                self.app_status_label.setObjectName("status-stopped")
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)
            
            # 更新功能开关
            self.message_listening_cb.setChecked(status.get('message_listening_enabled', True))
            self.auto_reply_cb.setChecked(status.get('auto_reply_enabled', True))
            self.music_notification_cb.setChecked(status.get('music_notification_enabled', True))
            self.phone_notification_cb.setChecked(status.get('phone_notification_enabled', True))
            
            # 更新统计信息
            self.messages_count_label.setText(str(status.get('total_messages', 0)))
            self.replies_count_label.setText(str(status.get('total_replies', 0)))
            self.music_count_label.setText(str(status.get('total_music_notifications', 0)))
            self.phone_count_label.setText(str(status.get('total_phone_notifications', 0)))
            
            # 重新应用样式
            self.style().unpolish(self)
            self.style().polish(self)
            
        except Exception as e:
            logger.error(f"更新状态显示失败: {str(e)}")

    def _format_connection_quality(self, quality):
        """格式化连接质量显示"""
        quality_map = {
            'good': '良好',
            'poor': '较差',
            'disconnected': '已断开',
            'unknown': '未知'
        }
        return quality_map.get(quality, '未知')

    # 事件处理方法
    def on_state_changed(self, data):
        """处理状态变化事件"""
        try:
            key = data.get('key')
            new_value = data.get('new_value')

            # 根据状态键更新对应的UI元素
            if key == 'is_running':
                self.update_running_status(new_value)
            elif key == 'websocket_connected':
                self.update_websocket_status(new_value)
            elif key == 'connection_quality':
                self.update_connection_quality(new_value)
            elif key in ['total_messages_received', 'total_replies_sent',
                        'total_music_notifications', 'total_phone_notifications']:
                self.update_statistics()

        except Exception as e:
            logger.error(f"处理状态变化事件失败: {str(e)}")

    def on_websocket_event(self, data):
        """处理WebSocket连接事件"""
        try:
            connected = data.get('connected', False)
            self.update_websocket_status(connected)
        except Exception as e:
            logger.error(f"处理WebSocket事件失败: {str(e)}")

    def on_error_event(self, data):
        """处理错误事件"""
        try:
            error_message = data.get('error', '')
            # 可以在这里显示错误通知或更新错误状态
            logger.debug(f"收到错误事件: {error_message}")
        except Exception as e:
            logger.error(f"处理错误事件失败: {str(e)}")

    def on_connection_quality_changed(self, data):
        """处理连接质量变化事件"""
        try:
            quality = data.get('quality', 'unknown')
            self.update_connection_quality(quality)
        except Exception as e:
            logger.error(f"处理连接质量事件失败: {str(e)}")

    def refresh_data(self):
        """刷新页面数据"""
        try:
            status = state_manager.get_status_summary()
            self.update_status(status)

            # 检查并更新应用运行状态
            self.update_app_running_status()
        except Exception as e:
            logger.error(f"刷新状态页面数据失败: {str(e)}")

    def update_app_running_status(self):
        """更新应用运行状态"""
        try:
            is_running = state_manager.is_main_application_running()
            if is_running:
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.app_status_label.setText("运行中")
                self.app_status_label.setObjectName("status-running")
            else:
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                self.app_status_label.setText("已停止")
                self.app_status_label.setObjectName("status-stopped")

            # 刷新样式
            self.app_status_label.style().unpolish(self.app_status_label)
            self.app_status_label.style().polish(self.app_status_label)

        except Exception as e:
            logger.error(f"更新应用运行状态失败: {str(e)}")

    def show_notification(self, message: str, notification_type: str = "info"):
        """显示通知消息"""
        try:
            # 创建临时标签显示通知
            if hasattr(self, 'notification_label'):
                self.notification_label.deleteLater()

            self.notification_label = QLabel(message)
            self.notification_label.setObjectName(f"notification-{notification_type}")

            # 设置通知样式
            if notification_type == "success":
                self.notification_label.setStyleSheet("""
                    QLabel#notification-success {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                        border-radius: 4px;
                        padding: 8px 12px;
                        margin: 5px 0;
                    }
                """)
            elif notification_type == "error":
                self.notification_label.setStyleSheet("""
                    QLabel#notification-error {
                        background-color: #f8d7da;
                        color: #721c24;
                        border: 1px solid #f5c6cb;
                        border-radius: 4px;
                        padding: 8px 12px;
                        margin: 5px 0;
                    }
                """)
            else:  # info
                self.notification_label.setStyleSheet("""
                    QLabel#notification-info {
                        background-color: #d1ecf1;
                        color: #0c5460;
                        border: 1px solid #bee5eb;
                        border-radius: 4px;
                        padding: 8px 12px;
                        margin: 5px 0;
                    }
                """)

            # 添加到布局顶部
            layout = self.layout()
            layout.insertWidget(1, self.notification_label)

            # 3秒后自动隐藏
            QTimer.singleShot(3000, self.hide_notification)

        except Exception as e:
            logger.error(f"显示通知失败: {str(e)}")

    def hide_notification(self):
        """隐藏通知"""
        try:
            if hasattr(self, 'notification_label'):
                self.notification_label.deleteLater()
                delattr(self, 'notification_label')
        except Exception as e:
            logger.error(f"隐藏通知失败: {str(e)}")

    # 具体的UI更新方法
    def update_running_status(self, is_running):
        """更新运行状态"""
        if hasattr(self, 'main_status_label'):
            if is_running:
                self.main_status_label.setText("运行中")
                self.main_status_label.setObjectName("status-running")
            else:
                self.main_status_label.setText("已停止")
                self.main_status_label.setObjectName("status-stopped")
            self.main_status_label.style().unpolish(self.main_status_label)
            self.main_status_label.style().polish(self.main_status_label)

    def update_websocket_status(self, connected):
        """更新WebSocket连接状态"""
        if hasattr(self, 'websocket_status_label'):
            if connected:
                self.websocket_status_label.setText("已连接")
                self.websocket_status_label.setObjectName("status-running")
            else:
                self.websocket_status_label.setText("未连接")
                self.websocket_status_label.setObjectName("status-stopped")
            self.websocket_status_label.style().unpolish(self.websocket_status_label)
            self.websocket_status_label.style().polish(self.websocket_status_label)

    def update_connection_quality(self, quality):
        """更新连接质量显示"""
        if hasattr(self, 'connection_quality_label'):
            quality_text = self._format_connection_quality(quality)
            self.connection_quality_label.setText(f"连接质量: {quality_text}")

            # 根据连接质量设置颜色
            if quality == 'good':
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #28a745;")
            elif quality == 'poor':
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #ffc107;")
            elif quality == 'disconnected':
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #dc3545;")
            else:
                self.connection_quality_label.setStyleSheet("font-size: 12px; color: #6c757d;")

    def update_statistics(self):
        """更新统计信息"""
        try:
            status = state_manager.get_status_summary()

            if hasattr(self, 'messages_count_label'):
                self.messages_count_label.setText(str(status.get('total_messages', 0)))

            if hasattr(self, 'replies_count_label'):
                self.replies_count_label.setText(str(status.get('total_replies', 0)))

            if hasattr(self, 'music_notifications_count_label'):
                self.music_notifications_count_label.setText(str(status.get('total_music_notifications', 0)))

            if hasattr(self, 'phone_notifications_count_label'):
                self.phone_notifications_count_label.setText(str(status.get('total_phone_notifications', 0)))

        except Exception as e:
            logger.error(f"更新统计信息失败: {str(e)}")
