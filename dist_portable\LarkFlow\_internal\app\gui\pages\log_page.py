"""
日志查看页面
实时显示日志，支持级别过滤、关键词搜索、自动刷新
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit,
    QComboBox, QCheckBox, QSpinBox, QFrame
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QTextCursor
from loguru import logger
import os
from pathlib import Path


class LogPage(QWidget):
    """日志查看页面"""
    
    def __init__(self):
        super().__init__()
        self.auto_refresh_enabled = False  # 默认关闭自动刷新
        self.init_ui()
        self.init_timer()
        logger.info("日志查看页面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("日志查看")
        title_label.setObjectName("title")
        layout.addWidget(title_label)
        
        # 创建控制面板
        self.create_control_panel(layout)
        
        # 创建日志显示区域
        self.create_log_display(layout)
        
        # 加载初始日志
        self.refresh_logs()
    
    def create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setObjectName("card")
        control_layout = QVBoxLayout(control_frame)
        
        # 第一行：过滤控件
        filter_layout = QHBoxLayout()
        
        # 日志级别过滤
        filter_layout.addWidget(QLabel("级别:"))
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_combo.setCurrentText("全部")
        self.level_combo.currentTextChanged.connect(self.on_filter_changed)
        filter_layout.addWidget(self.level_combo)
        
        # 搜索框
        filter_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索...")
        self.search_edit.textChanged.connect(self.on_filter_changed)
        filter_layout.addWidget(self.search_edit)
        
        # 显示行数
        filter_layout.addWidget(QLabel("行数:"))
        self.lines_spin = QSpinBox()
        self.lines_spin.setRange(50, 1000)
        self.lines_spin.setValue(200)
        self.lines_spin.valueChanged.connect(self.on_filter_changed)
        filter_layout.addWidget(self.lines_spin)
        
        filter_layout.addStretch()
        
        # 第二行：控制按钮
        button_layout = QHBoxLayout()
        
        # 自动刷新开关
        self.auto_refresh_cb = QCheckBox("自动刷新 (10秒间隔)")
        self.auto_refresh_cb.setChecked(False)  # 默认关闭
        self.auto_refresh_cb.toggled.connect(self.toggle_auto_refresh)
        button_layout.addWidget(self.auto_refresh_cb)
        
        # 手动刷新按钮
        refresh_button = QPushButton("刷新日志")
        refresh_button.clicked.connect(self.refresh_logs)
        button_layout.addWidget(refresh_button)
        
        # 清空日志按钮
        clear_button = QPushButton("清空显示")
        clear_button.setObjectName("secondary")
        clear_button.clicked.connect(self.clear_logs)
        button_layout.addWidget(clear_button)
        
        # 导出日志按钮
        export_button = QPushButton("导出日志")
        export_button.clicked.connect(self.export_logs)
        button_layout.addWidget(export_button)

        # 测试日志按钮
        test_button = QPushButton("测试日志")
        test_button.clicked.connect(self.test_log_display)
        button_layout.addWidget(test_button)

        button_layout.addStretch()
        
        control_layout.addLayout(filter_layout)
        control_layout.addLayout(button_layout)
        parent_layout.addWidget(control_frame)
    
    def create_log_display(self, parent_layout):
        """创建日志显示区域"""
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #3c3c3c;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        parent_layout.addWidget(self.log_text)
    
    def init_timer(self):
        """初始化定时器"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_logs)
        # 默认不启动自动刷新，用户可手动开启
    
    def toggle_auto_refresh(self, enabled):
        """切换自动刷新"""
        self.auto_refresh_enabled = enabled
        if enabled:
            self.refresh_timer.start(10000)  # 使用10秒间隔，减少频繁刷新
        else:
            self.refresh_timer.stop()

        logger.info(f"日志自动刷新: {'启用 (10秒间隔)' if enabled else '禁用'}")
    
    def on_filter_changed(self):
        """过滤条件变化"""
        if self.auto_refresh_enabled:
            self.refresh_logs()
    
    def refresh_logs(self):
        """刷新日志"""
        try:
            logs = self.read_log_files()
            self.display_logs(logs)
        except Exception as e:
            logger.error(f"刷新日志失败: {str(e)}")
    
    def read_log_files(self):
        """读取日志文件"""
        logs = []

        try:
            # 获取过滤参数
            level_filter = self.level_combo.currentText()
            search_query = self.search_edit.text().strip()
            max_lines = self.lines_spin.value()

            # 优先使用统一的日志文件，只有在找不到时才使用旧文件
            primary_log_files = [
                Path("logs/app.log"),  # 主日志文件（最高优先级）
            ]

            fallback_log_files = [
                Path("logs/error.log"),  # 错误日志文件
                Path("logs/gui_launcher.log"),  # 兼容旧的GUI日志文件
                Path("logs/launcher.log"),  # 兼容旧的Web日志文件
                Path("logs/main.log")  # 兼容旧的主程序日志文件
            ]

            lines = []
            found_files = []

            # 首先尝试读取主日志文件
            primary_file_found = False
            for log_file in primary_log_files:
                if log_file.exists():
                    try:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            file_lines = f.readlines()
                            if file_lines:  # 只有当文件有内容时才使用
                                lines.extend(file_lines)
                                found_files.append(str(log_file))
                                primary_file_found = True
                                # 移除频繁的调试信息，避免日志污染
                    except Exception as e:
                        logs.append(f"读取主日志文件 {log_file} 失败: {str(e)}")

            # 如果主日志文件不存在或为空，则使用备用日志文件
            if not primary_file_found:
                logger.warning("主日志文件不存在或为空，使用备用日志文件")
                for log_file in fallback_log_files:
                    if log_file.exists():
                        try:
                            with open(log_file, 'r', encoding='utf-8') as f:
                                file_lines = f.readlines()
                                lines.extend(file_lines)
                                found_files.append(str(log_file))
                        except Exception as e:
                            logs.append(f"读取备用日志文件 {log_file} 失败: {str(e)}")

            if not lines:
                return [
                    "未找到日志文件或日志文件为空",
                    "尝试查找的主日志文件: logs/app.log",
                    "尝试查找的备用文件: logs/error.log, logs/gui_launcher.log, logs/launcher.log",
                    *[f"  - {f}" for f in log_files],
                    "",
                    "请确保应用正在运行并生成日志"
                ]

            # 添加文件信息
            if found_files:
                from datetime import datetime
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                if primary_file_found:
                    logs.append(f"✅ 正在显示统一日志文件: {', '.join(found_files)}")
                    logs.append(f"📊 总共读取 {len(lines)} 行日志 (刷新时间: {current_time})")
                else:
                    logs.append(f"⚠️ 主日志文件不可用，显示备用日志: {', '.join(found_files)}")
                    logs.append(f"📊 总共读取 {len(lines)} 行日志 (刷新时间: {current_time})")
                logs.append(f"🔍 过滤条件: 级别={level_filter}, 搜索='{search_query}', 显示行数={max_lines}")
                logs.append("=" * 50)

            # 获取最后N行
            lines = lines[-max_lines:]
            original_count = len(lines)
            
            # 应用过滤
            filtered_count = 0
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 级别过滤
                if level_filter != "全部":
                    # 支持多种日志级别格式
                    level_patterns = [
                        # 方括号格式: [INFO], [INFO ], [ INFO ], etc.
                        f"[{level_filter}]",
                        f"[{level_filter} ]",
                        f"[ {level_filter}]",
                        f"[ {level_filter} ]",
                        # 竖线格式: | INFO |, | INFO     |, etc.
                        f"| {level_filter} |",
                        f"| {level_filter}     |",
                        f"|{level_filter}|",
                        # 其他可能的格式
                        f" {level_filter} ",
                        f"{level_filter}:"
                    ]
                    if not any(pattern in line for pattern in level_patterns):
                        continue

                # 搜索过滤
                if search_query:
                    if search_query.lower() not in line.lower():
                        continue

                logs.append(line)
                filtered_count += 1

            # 添加统计信息
            if found_files:
                logs.append("")
                logs.append("=" * 50)
                logs.append(f"显示 {filtered_count} 条日志 (原始: {original_count} 条)")
            
        except Exception as e:
            logs = [
                f"读取日志文件失败: {str(e)}",
                "",
                "故障排除建议:",
                "1. 检查 logs/ 目录是否存在",
                "2. 确保应用有写入日志的权限",
                "3. 尝试重启应用",
                "4. 点击'测试日志'按钮验证显示功能"
            ]

        return logs
    
    def display_logs(self, logs):
        """显示日志"""
        try:
            # 保存当前滚动位置
            scrollbar = self.log_text.verticalScrollBar()
            was_at_bottom = scrollbar.value() == scrollbar.maximum()
            
            # 清空并设置新内容
            self.log_text.clear()
            
            for log_line in logs:
                # 根据日志级别设置颜色 - 支持多种格式
                line_upper = log_line.upper()

                if any(pattern in line_upper for pattern in ["[ERROR", "ERROR]", "| ERROR", "CRITICAL"]):
                    color = "#ff6b6b"
                elif any(pattern in line_upper for pattern in ["[WARNING", "WARNING]", "| WARNING", "WARN"]):
                    color = "#ffd93d"
                elif any(pattern in line_upper for pattern in ["[INFO", "INFO]", "| INFO"]):
                    color = "#6bcf7f"
                elif any(pattern in line_upper for pattern in ["[DEBUG", "DEBUG]", "| DEBUG"]):
                    color = "#74c0fc"
                else:
                    color = "#d4d4d4"

                # 添加带颜色的日志行
                self.log_text.append(f'<span style="color: {color};">{log_line}</span>')
            
            # 如果之前在底部，保持在底部
            if was_at_bottom:
                scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            logger.error(f"显示日志失败: {str(e)}")
    
    def clear_logs(self):
        """清空日志显示"""
        self.log_text.clear()
        logger.info("日志显示已清空")
    
    def export_logs(self):
        """导出日志"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            
            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出日志",
                f"larkflow_logs_{self.get_timestamp()}.txt",
                "文本文件 (*.txt);;所有文件 (*)"
            )
            
            if file_path:
                # 获取当前显示的日志
                logs = self.read_log_files()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"LarkFlow 日志导出\n")
                    f.write(f"导出时间: {self.get_timestamp()}\n")
                    f.write(f"日志级别: {self.level_combo.currentText()}\n")
                    f.write(f"搜索关键词: {self.search_edit.text()}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for log_line in logs:
                        f.write(log_line + "\n")
                
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.information(self, "成功", f"日志已导出到: {file_path}")
                logger.info(f"日志导出成功: {file_path}")
        
        except Exception as e:
            logger.error(f"导出日志失败: {str(e)}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"导出日志失败: {str(e)}")
    
    def get_timestamp(self):
        """获取时间戳字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def test_log_display(self):
        """测试日志显示功能"""
        try:
            from datetime import datetime

            # 生成测试日志
            test_logs = [
                f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} [INFO ] 这是一条测试信息日志",
                f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} [WARNING] 这是一条测试警告日志",
                f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} [ERROR] 这是一条测试错误日志",
                f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} [DEBUG] 这是一条测试调试日志",
                f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} [INFO ] GUI日志显示功能测试完成",
                "",
                "如果您能看到这些彩色的测试日志，说明日志显示功能正常工作。",
                "如果看不到实际的应用日志，请检查:",
                "1. 日志文件是否存在于 logs/ 目录",
                "2. 应用是否正在运行并生成日志",
                "3. 日志文件是否有读取权限"
            ]

            # 显示测试日志
            self.display_logs(test_logs)

            # 记录测试日志并生成实际的日志条目
            logger.info("用户执行了日志显示测试")

            # 生成实际的测试日志条目
            from app.utils.logger_config import test_logging
            test_logging()

            # 延迟刷新以显示新生成的日志
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(1000, self.refresh_logs)

        except Exception as e:
            logger.error(f"测试日志显示失败: {str(e)}")
            self.log_text.setText(f"测试日志显示失败: {str(e)}")

    def refresh_data(self):
        """刷新页面数据"""
        self.refresh_logs()
