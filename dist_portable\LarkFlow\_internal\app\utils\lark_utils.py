
import os
import sys
import subprocess
from pathlib import Path
from loguru import logger
from functools import partial

# 配置subprocess以避免弹窗
subprocess.Popen = partial(subprocess.Popen, encoding="utf-8",
                          creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)

# 全局JavaScript环境缓存
_js_context = None

def _setup_bundled_nodejs():
    """
    设置打包的Node.js环境路径
    """
    try:
        # 获取程序目录
        if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
            # 打包后环境
            app_dir = Path(sys._MEIPASS)
        else:
            # 开发环境
            app_dir = Path(__file__).parent.parent.parent

        # 检查打包的Node.js路径
        nodejs_paths = [
            app_dir / 'nodejs' / 'node.exe',  # Windows
            app_dir / 'nodejs' / 'node',      # Linux/Mac
            Path('.') / 'nodejs' / 'node.exe',  # 相对路径Windows
            Path('.') / 'nodejs' / 'node',      # 相对路径Linux/Mac
        ]

        for nodejs_path in nodejs_paths:
            if nodejs_path.exists():
                # 将Node.js目录添加到PATH环境变量的最前面
                nodejs_dir = str(nodejs_path.parent)
                current_path = os.environ.get('PATH', '')

                # 如果还没有添加过，就添加到PATH前面
                if nodejs_dir not in current_path:
                    os.environ['PATH'] = f"{nodejs_dir}{os.pathsep}{current_path}"
                    logger.info(f"已设置打包的Node.js路径: {nodejs_path}")
                return True

        logger.info("未找到打包的Node.js，将使用系统默认JavaScript引擎")
        return False

    except Exception as e:
        logger.warning(f"设置Node.js路径失败: {e}")
        return False

def get_js_context():
    """
    获取JavaScript环境（使用缓存避免重复初始化）
    """
    global _js_context

    if _js_context is not None:
        return _js_context

    try:
        # 延迟导入execjs，避免启动时的弹窗
        import execjs

        # 配置打包的Node.js路径（优先使用）
        _setup_bundled_nodejs()

        # 配置execjs使用静默模式
        if os.name == 'nt':  # Windows系统
            # 设置环境变量以避免弹窗
            old_pathext = os.environ.get("PATHEXT", "")
            if '.EXE' not in old_pathext:
                os.environ['PATHEXT'] = old_pathext + '.EXE;'

        # 获取JavaScript文件路径
        if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
            # 打包后环境
            script_dir = Path(sys._MEIPASS)
        else:
            # 开发环境
            script_dir = Path(__file__).parent.parent.parent

        js_path = script_dir / 'static' / 'lark_decrypt.js'

        if js_path.exists():
            with open(js_path, 'r', encoding='utf-8') as f:
                js_content = f.read()

            # 尝试编译JavaScript
            _js_context = execjs.compile(js_content)
            logger.info(f"JavaScript环境初始化成功，文件: {js_path}")
            return _js_context
        else:
            logger.warning(f"JavaScript文件不存在: {js_path}")
            # 尝试其他可能的路径
            alternative_paths = [
                Path('.') / 'static' / 'lark_decrypt.js',
                Path('static') / 'lark_decrypt.js'
            ]

            for alt_path in alternative_paths:
                if alt_path.exists():
                    with open(alt_path, 'r', encoding='utf-8') as f:
                        js_content = f.read()
                    _js_context = execjs.compile(js_content)
                    logger.info(f"JavaScript环境初始化成功，备用路径: {alt_path}")
                    return _js_context

            logger.error("所有JavaScript文件路径都不存在")
            return None

    except Exception as e:
        logger.error(f"初始化JavaScript环境失败: {e}")
        return None

def init_js():
    """
    Initialize JavaScript environment for Lark decryption (兼容性函数)
    """
    return get_js_context()


def trans_cookies(cookies_str):
    """
    Transform cookie string to dictionary
    
    Args:
        cookies_str (str): Cookie string
        
    Returns:
        dict: Cookie dictionary
    """
    cookies = dict()
    for i in cookies_str.split("; "):
        try:
            cookies[i.split('=')[0]] = '='.join(i.split('=')[1:])
        except:
            continue
    return cookies

def generate_access_key(mystr):
    """Generate access key using JavaScript function"""
    js_context = get_js_context()
    if js_context:
        try:
            access_key = js_context.call('generate_access_key', mystr)
            return access_key
        except Exception as e:
            logger.error(f"生成access_key失败: {e}")
    return None

def generate_request_id():
    """Generate request ID using JavaScript function"""
    js_context = get_js_context()
    if js_context:
        try:
            request_id = js_context.call('generate_request_id')
            return request_id
        except Exception as e:
            logger.error(f"生成request_id失败: {e}")
    return None

def generate_long_request_id():
    """Generate long request ID using JavaScript function"""
    js_context = get_js_context()

    if js_context:
        try:
            request_id = js_context.call('generate_long_request_id')
            return request_id
        except Exception as e:
            logger.error(f"生成long_request_id失败: {e}")
    return None

def generate_request_cid():
    """Generate request CID using JavaScript function"""
    js_context = get_js_context()
    if js_context:
        try:
            request_cid = js_context.call('generate_request_cid')
            return request_cid
        except Exception as e:
            logger.error(f"生成request_cid失败: {e}")
    return None
