"""
GUI动画效果管理器
提供流畅的界面动画和过渡效果
"""
from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QRect, QPoint, QSize, pyqtProperty
from PyQt6.QtWidgets import QWidget, QGraphicsOpacityEffect
from PyQt6.QtGui import QColor
from loguru import logger


class AnimationManager:
    """动画管理器"""
    
    def __init__(self):
        self.animations = []
    
    def fade_in(self, widget: QWidget, duration: int = 300):
        """淡入动画"""
        try:
            effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(effect)
            
            animation = QPropertyAnimation(effect, b"opacity")
            animation.setDuration(duration)
            animation.setStartValue(0.0)
            animation.setEndValue(1.0)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            
            self.animations.append(animation)
            animation.start()
            
            return animation
        except Exception as e:
            logger.error(f"淡入动画失败: {str(e)}")
            return None
    
    def fade_out(self, widget: QWidget, duration: int = 300):
        """淡出动画"""
        try:
            effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(effect)
            
            animation = QPropertyAnimation(effect, b"opacity")
            animation.setDuration(duration)
            animation.setStartValue(1.0)
            animation.setEndValue(0.0)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            
            self.animations.append(animation)
            animation.start()
            
            return animation
        except Exception as e:
            logger.error(f"淡出动画失败: {str(e)}")
            return None
    
    def slide_in_from_left(self, widget: QWidget, duration: int = 400):
        """从左侧滑入动画"""
        try:
            start_pos = widget.pos()
            start_pos.setX(start_pos.x() - widget.width())
            end_pos = widget.pos()
            
            animation = QPropertyAnimation(widget, b"pos")
            animation.setDuration(duration)
            animation.setStartValue(start_pos)
            animation.setEndValue(end_pos)
            animation.setEasingCurve(QEasingCurve.Type.OutQuart)
            
            self.animations.append(animation)
            animation.start()
            
            return animation
        except Exception as e:
            logger.error(f"滑入动画失败: {str(e)}")
            return None
    
    def bounce_in(self, widget: QWidget, duration: int = 600):
        """弹跳进入动画"""
        try:
            original_size = widget.size()
            
            # 缩放动画
            animation = QPropertyAnimation(widget, b"size")
            animation.setDuration(duration)
            animation.setStartValue(QSize(0, 0))
            animation.setEndValue(original_size)
            animation.setEasingCurve(QEasingCurve.Type.OutBounce)
            
            self.animations.append(animation)
            animation.start()
            
            return animation
        except Exception as e:
            logger.error(f"弹跳动画失败: {str(e)}")
            return None
    
    def pulse_effect(self, widget: QWidget, duration: int = 1000):
        """脉冲效果动画"""
        try:
            effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(effect)
            
            animation = QPropertyAnimation(effect, b"opacity")
            animation.setDuration(duration)
            animation.setStartValue(1.0)
            animation.setEndValue(0.3)
            animation.setEasingCurve(QEasingCurve.Type.InOutSine)
            animation.setLoopCount(-1)  # 无限循环
            
            # 设置往返动画
            animation.finished.connect(lambda: animation.setDirection(
                QPropertyAnimation.Direction.Backward if animation.direction() == QPropertyAnimation.Direction.Forward 
                else QPropertyAnimation.Direction.Forward
            ))
            
            self.animations.append(animation)
            animation.start()
            
            return animation
        except Exception as e:
            logger.error(f"脉冲动画失败: {str(e)}")
            return None
    
    def shake_effect(self, widget: QWidget, duration: int = 500):
        """摇摆效果动画"""
        try:
            original_pos = widget.pos()
            
            animation = QPropertyAnimation(widget, b"pos")
            animation.setDuration(duration)
            animation.setEasingCurve(QEasingCurve.Type.InOutSine)
            
            # 设置关键帧
            animation.setKeyValueAt(0.0, original_pos)
            animation.setKeyValueAt(0.1, QPoint(original_pos.x() + 10, original_pos.y()))
            animation.setKeyValueAt(0.2, QPoint(original_pos.x() - 10, original_pos.y()))
            animation.setKeyValueAt(0.3, QPoint(original_pos.x() + 8, original_pos.y()))
            animation.setKeyValueAt(0.4, QPoint(original_pos.x() - 8, original_pos.y()))
            animation.setKeyValueAt(0.5, QPoint(original_pos.x() + 5, original_pos.y()))
            animation.setKeyValueAt(0.6, QPoint(original_pos.x() - 5, original_pos.y()))
            animation.setKeyValueAt(0.7, QPoint(original_pos.x() + 3, original_pos.y()))
            animation.setKeyValueAt(0.8, QPoint(original_pos.x() - 3, original_pos.y()))
            animation.setKeyValueAt(0.9, QPoint(original_pos.x() + 1, original_pos.y()))
            animation.setKeyValueAt(1.0, original_pos)
            
            self.animations.append(animation)
            animation.start()
            
            return animation
        except Exception as e:
            logger.error(f"摇摆动画失败: {str(e)}")
            return None
    
    def smooth_resize(self, widget: QWidget, new_size: QSize, duration: int = 300):
        """平滑调整大小动画"""
        try:
            animation = QPropertyAnimation(widget, b"size")
            animation.setDuration(duration)
            animation.setStartValue(widget.size())
            animation.setEndValue(new_size)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            
            self.animations.append(animation)
            animation.start()
            
            return animation
        except Exception as e:
            logger.error(f"调整大小动画失败: {str(e)}")
            return None
    
    def stop_all_animations(self):
        """停止所有动画"""
        try:
            for animation in self.animations:
                if animation.state() == QPropertyAnimation.State.Running:
                    animation.stop()
            self.animations.clear()
            logger.debug("所有动画已停止")
        except Exception as e:
            logger.error(f"停止动画失败: {str(e)}")
    
    def cleanup_finished_animations(self):
        """清理已完成的动画"""
        try:
            self.animations = [
                anim for anim in self.animations 
                if anim.state() != QPropertyAnimation.State.Stopped
            ]
        except Exception as e:
            logger.error(f"清理动画失败: {str(e)}")


class AnimatedWidget(QWidget):
    """支持动画的Widget基类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.animation_manager = AnimationManager()
        self._opacity = 1.0
    
    def get_opacity(self):
        return self._opacity
    
    def set_opacity(self, opacity):
        self._opacity = opacity
        self.update()
    
    opacity = pyqtProperty(float, get_opacity, set_opacity)
    
    def fade_in(self, duration: int = 300):
        """淡入显示"""
        return self.animation_manager.fade_in(self, duration)
    
    def fade_out(self, duration: int = 300):
        """淡出隐藏"""
        return self.animation_manager.fade_out(self, duration)
    
    def slide_in(self, duration: int = 400):
        """滑入显示"""
        return self.animation_manager.slide_in_from_left(self, duration)
    
    def bounce_in(self, duration: int = 600):
        """弹跳显示"""
        return self.animation_manager.bounce_in(self, duration)
    
    def shake(self, duration: int = 500):
        """摇摆效果"""
        return self.animation_manager.shake_effect(self, duration)
    
    def pulse(self, duration: int = 1000):
        """脉冲效果"""
        return self.animation_manager.pulse_effect(self, duration)


# 全局动画管理器实例
global_animation_manager = AnimationManager()


# 便捷函数
def animate_fade_in(widget: QWidget, duration: int = 300):
    """便捷的淡入动画函数"""
    return global_animation_manager.fade_in(widget, duration)


def animate_fade_out(widget: QWidget, duration: int = 300):
    """便捷的淡出动画函数"""
    return global_animation_manager.fade_out(widget, duration)


def animate_slide_in(widget: QWidget, duration: int = 400):
    """便捷的滑入动画函数"""
    return global_animation_manager.slide_in_from_left(widget, duration)


def animate_bounce_in(widget: QWidget, duration: int = 600):
    """便捷的弹跳动画函数"""
    return global_animation_manager.bounce_in(widget, duration)


def animate_shake(widget: QWidget, duration: int = 500):
    """便捷的摇摆动画函数"""
    return global_animation_manager.shake_effect(widget, duration)


def animate_pulse(widget: QWidget, duration: int = 1000):
    """便捷的脉冲动画函数"""
    return global_animation_manager.pulse_effect(widget, duration)
