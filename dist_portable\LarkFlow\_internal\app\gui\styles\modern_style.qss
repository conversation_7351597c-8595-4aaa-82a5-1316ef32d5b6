/* LarkFlow 简洁样式表 */

/* 全局样式 */
QWidget {
    font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
    color: #333333;
    background-color: #ffffff;
}

/* 主窗口 */
QMainWindow {
    background-color: #fafafa;
    border: none;
}

/* 导航栏框架 */
QFrame#nav_frame {
    background-color: #ffffff;
    border-right: 1px solid #e0e0e0;
}

/* 导航栏 */
QListWidget#nav_list {
    background: transparent;
    border: none;
    outline: none;
    padding: 10px 0;
    min-width: 200px;
    max-width: 200px;
}

QListWidget#nav_list::item {
    padding: 12px 20px;
    margin: 2px 8px;
    border-radius: 6px;
    color: #666666;
    font-weight: 500;
}

QListWidget#nav_list::item:hover {
    background-color: #f5f5f5;
    color: #333333;
}

QListWidget#nav_list::item:selected {
    background-color: #007bff;
    color: white;
    font-weight: 600;
}

/* 内容区域 */
QStackedWidget {
    background-color: #f8f9fa;
    border: none;
}

/* 卡片样式 */
QFrame[objectName="card"] {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
}

/* 按钮样式 */
QPushButton {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-weight: 500;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #0056b3;
}

QPushButton:pressed {
    background-color: #004085;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #999999;
}

/* 次要按钮 */
QPushButton[objectName="secondary"] {
    background-color: #6c757d;
}

QPushButton[objectName="secondary"]:hover {
    background-color: #545b62;
}

/* 成功按钮 */
QPushButton[objectName="success"] {
    background-color: #28a745;
}

QPushButton[objectName="success"]:hover {
    background-color: #1e7e34;
}

/* 危险按钮 */
QPushButton[objectName="danger"] {
    background-color: #dc3545;
}

QPushButton[objectName="danger"]:hover {
    background-color: #c82333;
}

/* 输入框 */
QLineEdit, QTextEdit, QPlainTextEdit {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 8px 12px;
    background-color: white;
    color: #333333;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 1px solid #007bff;
    outline: none;
}

/* 标签 */
QLabel {
    color: #333333;
}

QLabel[objectName="title"], QLabel[objectName="page_title"] {
    font-size: 16pt;
    font-weight: 600;
    color: #222222;
    margin-bottom: 8px;
}

QLabel[objectName="subtitle"] {
    font-size: 12pt;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 15px;
}

/* 开关按钮 */
QCheckBox {
    spacing: 8px;
    color: #495057;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border-radius: 3px;
    border: 2px solid #ced4da;
    background-color: white;
}

QCheckBox::indicator:checked {
    background-color: #007bff;
    border-color: #007bff;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #f8f9fa;
    width: 8px;
    border-radius: 4px;
}

QScrollBar::handle:vertical {
    background-color: #ced4da;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #adb5bd;
}

/* 分组框 */
QGroupBox {
    font-weight: 600;
    color: #495057;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px;
    background-color: #f8f9fa;
}

/* 状态指示器 */
QLabel[objectName="status-running"] {
    color: #28a745;
    font-weight: 600;
}

QLabel[objectName="status-stopped"] {
    color: #dc3545;
    font-weight: 600;
}

QLabel[objectName="status-warning"] {
    color: #ffc107;
    font-weight: 600;
}

/* 统计数字 */
QLabel[objectName="metric-number"] {
    font-size: 24pt;
    font-weight: 700;
    color: #007bff;
}

QLabel[objectName="metric-label"] {
    font-size: 10pt;
    color: #6c757d;
    font-weight: 500;
}

/* 复选框样式 */
QCheckBox {
    spacing: 6px;
    color: #333333;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #cccccc;
    background: white;
}

QCheckBox::indicator:checked {
    background-color: #007bff;
    border: 1px solid #007bff;
}

/* 分组框 */
QGroupBox {
    font-weight: 600;
    color: #333333;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-top: 8px;
    padding-top: 12px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 6px;
    background-color: #fafafa;
}
