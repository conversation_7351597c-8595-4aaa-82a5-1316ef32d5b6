"""
LarkFlow 主窗口
基于PyQt6的现代化桌面界面
"""
import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
    QListWidget, QStackedWidget, QListWidgetItem,
    QSystemTrayIcon, QMenu, QApplication, QMessageBox,
    QLabel, QFrame
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap, QAction
from loguru import logger

# 导入页面组件
from app.gui.pages.status_page import StatusPage
from app.gui.pages.config_page import ConfigPage
from app.gui.pages.log_page import LogPage
from app.gui.pages.audio_page import AudioPage

# 导入样式管理器
from app.gui.styles.style_manager import style_manager

# 导入系统托盘
from app.gui.widgets.system_tray import SystemTray

# 导入现有功能模块
from app.core.state_manager import state_manager
from app.core.config_manager import config_manager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    status_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("飞书智能助手")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 初始化UI
        self.init_ui()
        
        # 初始化系统托盘
        self.init_system_tray()
        
        # 初始化定时器
        self.init_timers()
        
        # 连接信号
        self.connect_signals()
        
        logger.info("主窗口初始化完成")
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取可执行文件所在目录
            if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
                # 打包后环境：先尝试exe同目录，再尝试临时目录
                exe_dir = Path(sys.executable).parent
                temp_dir = Path(sys._MEIPASS)
                icon_paths = [
                    exe_dir / "feishu.ico",
                    temp_dir / "feishu.ico",
                    exe_dir / "static" / "feishu.ico"
                ]
            else:
                # 开发环境
                base_path = Path(__file__).parent.parent.parent
                icon_paths = [
                    base_path / "feishu.ico",
                    Path("feishu.ico"),
                    base_path / "static" / "feishu.ico"
                ]

            icon_set = False
            for icon_path in icon_paths:
                if icon_path.exists():
                    self.setWindowIcon(QIcon(str(icon_path)))
                    logger.info(f"使用窗口图标: {icon_path}")
                    icon_set = True
                    break

            if not icon_set:
                logger.warning("未找到图标文件，使用默认图标")

        except Exception as e:
            logger.error(f"设置窗口图标失败: {str(e)}")
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建导航栏
        self.create_navigation()
        
        # 创建内容区域
        self.create_content_area()
        
        # 添加到主布局
        main_layout.addWidget(self.nav_widget)
        main_layout.addWidget(self.content_widget)
    
    def create_navigation(self):
        """创建导航栏"""
        self.nav_widget = QFrame()
        self.nav_widget.setFixedWidth(200)
        self.nav_widget.setObjectName("nav_frame")
        
        nav_layout = QVBoxLayout(self.nav_widget)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(0)
        
        # 应用标题
        title_label = QLabel("LarkFlow")
        title_label.setObjectName("app_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel#app_title {
                font-size: 16pt;
                font-weight: 600;
                color: #007bff;
                padding: 20px;
                background-color: white;
                border-bottom: 1px solid #e0e0e0;
            }
        """)
        
        # 导航列表
        self.nav_list = QListWidget()
        self.nav_list.setObjectName("nav_list")
        
        # 添加导航项
        nav_items = [
            ("📊  状态概览", "status"),
            ("⚙️  配置管理", "config"),
            ("📋  日志查看", "logs"),
            ("🎵  音频管理", "audio"),
        ]
        
        for text, data in nav_items:
            item = QListWidgetItem(text)
            item.setData(Qt.ItemDataRole.UserRole, data)
            self.nav_list.addItem(item)
        
        # 设置默认选中第一项
        self.nav_list.setCurrentRow(0)
        
        nav_layout.addWidget(title_label)
        nav_layout.addWidget(self.nav_list)
        nav_layout.addStretch()
    
    def create_content_area(self):
        """创建内容区域"""
        self.content_widget = QFrame()
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建页面堆栈
        self.page_stack = QStackedWidget()
        
        # 创建各个页面
        self.status_page = StatusPage()
        self.config_page = ConfigPage()
        self.log_page = LogPage()
        self.audio_page = AudioPage()
        
        # 添加页面到堆栈
        self.page_stack.addWidget(self.status_page)
        self.page_stack.addWidget(self.config_page)
        self.page_stack.addWidget(self.log_page)
        self.page_stack.addWidget(self.audio_page)
        
        content_layout.addWidget(self.page_stack)
    
    def init_system_tray(self):
        """初始化系统托盘"""
        self.system_tray = SystemTray(self)

        # 连接托盘信号
        self.system_tray.show_window_requested.connect(self.show_window)
        self.system_tray.hide_window_requested.connect(self.hide)
        self.system_tray.quit_requested.connect(self.quit_application)
    
    def init_timers(self):
        """初始化定时器"""
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(3000)  # 每3秒更新一次
    
    def connect_signals(self):
        """连接信号槽"""
        # 导航列表选择变化
        self.nav_list.currentRowChanged.connect(self.on_nav_changed)
        
        # 状态更新信号
        self.status_updated.connect(self.on_status_updated)
    
    def on_nav_changed(self, index):
        """导航选择变化处理"""
        self.page_stack.setCurrentIndex(index)
        
        # 更新页面数据
        current_page = self.page_stack.currentWidget()
        if hasattr(current_page, 'refresh_data'):
            current_page.refresh_data()
    
    def update_status(self):
        """更新状态信息"""
        try:
            status = state_manager.get_status_summary()
            self.status_updated.emit(status)
        except Exception as e:
            logger.error(f"更新状态失败: {str(e)}")
    
    def on_status_updated(self, status):
        """状态更新处理"""
        # 更新状态页面
        if hasattr(self.status_page, 'update_status'):
            self.status_page.update_status(status)

        # 更新系统托盘
        if hasattr(self, 'system_tray'):
            self.system_tray.update_status(status)
    
    def show_window(self):
        """显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if hasattr(self, 'system_tray'):
            # 最小化到托盘
            self.hide()
            event.ignore()

            # 显示提示消息（仅第一次）
            if not hasattr(self, '_tray_message_shown'):
                self.system_tray.show_message(
                    "LarkFlow",
                    "程序已最小化到系统托盘",
                    QSystemTrayIcon.MessageIcon.Information
                )
                self._tray_message_shown = True
        else:
            # 直接退出
            self.quit_application()
    
    def quit_application(self):
        """退出应用程序"""
        reply = QMessageBox.question(
            self, 
            "确认退出", 
            "确定要退出 LarkFlow 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 停止主应用
            try:
                state_manager.stop_main_application()
            except Exception as e:
                logger.error(f"停止主应用失败: {str(e)}")
            
            # 退出程序
            QApplication.quit()
