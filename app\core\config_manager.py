"""
配置管理器
用于读取、修改和保存.env配置文件
（已从app/web/移动到app/core/，供GUI和核心功能共享使用）
"""
import os
import re
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, env_file: str = ".env"):
        # 确保使用项目根目录的.env文件
        if not os.path.isabs(env_file):
            # 获取项目根目录（config_manager.py的上两级目录）
            project_root = Path(__file__).parent.parent.parent
            self.env_file = project_root / env_file
        else:
            self.env_file = Path(env_file)
        self.config_cache = {}
        self.load_config()
    
    def load_config(self) -> Dict[str, str]:
        """加载配置文件"""
        config = {}
        
        if not self.env_file.exists():
            logger.warning(f"配置文件不存在: {self.env_file}")
            return config
        
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析键值对
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"\'')
                        config[key] = value
                    
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
        
        self.config_cache = config
        return config
    
    def get_config(self, key: str, default: str = "") -> str:
        """获取配置值"""
        return self.config_cache.get(key, default)
    
    def set_config(self, key: str, value: str) -> bool:
        """设置配置值"""
        try:
            self.config_cache[key] = value
            return self.save_config()
        except Exception as e:
            logger.error(f"设置配置失败: {str(e)}")
            return False
    
    def get_all_config(self) -> Dict[str, str]:
        """获取所有配置"""
        return self.config_cache.copy()
    
    def update_config(self, updates: Dict[str, str]) -> bool:
        """批量更新配置"""
        try:
            self.config_cache.update(updates)
            return self.save_config()
        except Exception as e:
            logger.error(f"批量更新配置失败: {str(e)}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            # 读取原文件内容，保持注释和格式
            original_lines = []
            if self.env_file.exists():
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    original_lines = f.readlines()
            
            # 构建新的文件内容
            new_lines = []
            processed_keys = set()
            
            # 处理原有行，更新已存在的配置
            for line in original_lines:
                stripped = line.strip()
                if not stripped or stripped.startswith('#'):
                    # 保持注释和空行
                    new_lines.append(line)
                elif '=' in stripped:
                    # 处理配置行
                    key = stripped.split('=', 1)[0].strip()
                    if key in self.config_cache:
                        # 更新配置值
                        value = self.config_cache[key]
                        new_lines.append(f'{key}="{value}"\n')
                        processed_keys.add(key)
                    else:
                        # 保持原有配置
                        new_lines.append(line)
                else:
                    # 保持其他行
                    new_lines.append(line)
            
            # 添加新的配置项
            for key, value in self.config_cache.items():
                if key not in processed_keys:
                    new_lines.append(f'{key}="{value}"\n')
            
            # 写入文件
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            logger.info("配置文件保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
            return False
    
    def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            self.load_config()
            logger.info("配置重新加载成功")
            return True
        except Exception as e:
            logger.error(f"重新加载配置失败: {str(e)}")
            return False
    
    def get_config_schema(self) -> Dict[str, Dict[str, Any]]:
        """获取配置项的结构定义"""
        return {
            "LARK_COOKIE": {
                "name": "飞书Cookie",
                "description": "飞书网页版的Cookie，用于身份认证",
                "type": "password",
                "required": True,
                "category": "认证配置"
            },
            "TRIGGER_PATTERN": {
                "name": "触发模式",
                "description": "消息匹配的正则表达式模式",
                "type": "text",
                "required": False,
                "category": "消息配置",
                "default": "已接通人工.*?为你服务.*?请问.*?帮你"
            },
            "REPLY_MESSAGE": {
                "name": "回复消息",
                "description": "自动回复的消息内容",
                "type": "text",
                "required": False,
                "category": "消息配置",
                "default": "您好！有什么可以帮您？"
            },
            "ENABLE_MUSIC_NOTIFICATION": {
                "name": "启用音乐提醒",
                "description": "是否启用音乐提醒功能",
                "type": "boolean",
                "required": False,
                "category": "提醒配置",
                "default": "true"
            },
            "NOTIFICATION_MUSIC_FILE": {
                "name": "提醒音乐文件",
                "description": "音乐提醒使用的音频文件路径",
                "type": "text",
                "required": False,
                "category": "提醒配置",
                "default": "static/audio/notification.mp3"
            },
            "NOTIFICATION_TITLE": {
                "name": "提醒标题",
                "description": "弹窗提醒的标题",
                "type": "text",
                "required": False,
                "category": "提醒配置",
                "default": "飞书消息提醒"
            },
            "NOTIFICATION_MESSAGE": {
                "name": "提醒消息",
                "description": "弹窗提醒的消息内容",
                "type": "text",
                "required": False,
                "category": "提醒配置",
                "default": "您有新的飞书消息！\\n点击确定停止音乐提醒"
            },
            "ENABLE_PHONE_NOTIFICATION": {
                "name": "启用电话提醒",
                "description": "是否启用电话提醒功能",
                "type": "boolean",
                "required": False,
                "category": "电话提醒",
                "default": "false"
            },
            "LARK_APP_ID": {
                "name": "飞书应用ID",
                "description": "飞书开放平台应用的App ID",
                "type": "password",
                "required": False,
                "category": "电话提醒"
            },
            "LARK_APP_SECRET": {
                "name": "飞书应用密钥",
                "description": "飞书开放平台应用的App Secret",
                "type": "password",
                "required": False,
                "category": "电话提醒"
            },
            "PHONE_NOTIFICATION_USER_IDS": {
                "name": "电话提醒用户ID",
                "description": "接收电话提醒的用户ID列表，用逗号分隔",
                "type": "text",
                "required": False,
                "category": "电话提醒"
            },
            "PHONE_NOTIFICATION_MESSAGE_IDS": {
                "name": "电话提醒消息ID",
                "description": "电话提醒使用的消息ID列表，用逗号分隔",
                "type": "text",
                "required": False,
                "category": "电话提醒"
            },
            "PHONE_NOTIFICATION_INTERVAL": {
                "name": "电话提醒间隔",
                "description": "电话提醒的时间间隔（秒）",
                "type": "number",
                "required": False,
                "category": "电话提醒",
                "default": "15"
            },
            "MESSAGE_MATCH_MODE": {
                "name": "消息匹配模式",
                "description": "消息匹配模式：content（内容匹配）或group_name（群聊名称匹配）",
                "type": "select",
                "options": [
                    {"value": "content", "label": "内容匹配模式"},
                    {"value": "group_name", "label": "群聊名称匹配模式"}
                ],
                "required": False,
                "category": "消息配置",
                "default": "content"
            },
            "GROUP_NAME_PATTERN": {
                "name": "群聊名称模式",
                "description": "群聊名称匹配的正则表达式模式",
                "type": "text",
                "required": False,
                "category": "消息配置"
            },
            "GROUP_NAME_REPLY_MESSAGE": {
                "name": "群聊回复消息",
                "description": "群聊名称匹配时的回复消息",
                "type": "text",
                "required": False,
                "category": "消息配置"
            },
            "GROUP_NAME_CONTENT_PATTERN": {
                "name": "群聊内容模式",
                "description": "群聊内容匹配的正则表达式模式",
                "type": "text",
                "required": False,
                "category": "消息配置"
            }
        }
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置的完整性"""
        errors = []
        warnings = []
        
        schema = self.get_config_schema()
        
        # 检查必需配置
        for key, info in schema.items():
            if info.get("required", False):
                value = self.get_config(key)
                if not value or value.strip() == "":
                    errors.append(f"缺少必需配置: {info['name']} ({key})")
        
        # 检查布尔值配置
        boolean_keys = [k for k, v in schema.items() if v.get("type") == "boolean"]
        for key in boolean_keys:
            value = self.get_config(key)
            if value and value.lower() not in ["true", "false", "1", "0", "yes", "no"]:
                warnings.append(f"布尔配置值可能不正确: {key}={value}")
        
        # 检查数字配置
        number_keys = [k for k, v in schema.items() if v.get("type") == "number"]
        for key in number_keys:
            value = self.get_config(key)
            if value:
                try:
                    float(value)
                except ValueError:
                    errors.append(f"数字配置值格式错误: {key}={value}")
        
        return {"errors": errors, "warnings": warnings}


# 全局配置管理器实例
config_manager = ConfigManager()
