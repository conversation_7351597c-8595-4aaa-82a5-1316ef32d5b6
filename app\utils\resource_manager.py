"""
资源管理器模块
统一管理WebSocket连接、线程、事件循环等资源的创建和清理
"""
import threading
import asyncio
import weakref
from typing import List, Tuple, Callable, Any, Optional
from loguru import logger


class ResourceManager:
    """统一资源管理器"""
    
    def __init__(self, name: str = "ResourceManager"):
        self.name = name
        self._resources: List[Tuple[Any, Callable[[Any], None], str]] = []
        self._lock = threading.RLock()
        self._cleanup_callbacks: List[Callable[[], None]] = []
        
    def register(self, resource: Any, cleanup_func: Callable[[Any], None], description: str = "") -> None:
        """注册需要清理的资源"""
        with self._lock:
            self._resources.append((resource, cleanup_func, description or str(type(resource).__name__)))
            logger.debug(f"[{self.name}] 注册资源: {description}")
    
    def register_callback(self, callback: Callable[[], None]) -> None:
        """注册清理回调函数"""
        with self._lock:
            self._cleanup_callbacks.append(callback)
            logger.debug(f"[{self.name}] 注册清理回调")
    
    def cleanup_all(self) -> None:
        """清理所有注册的资源"""
        logger.info(f"[{self.name}] 开始清理资源...")
        
        with self._lock:
            # 先执行清理回调
            for callback in reversed(self._cleanup_callbacks):
                try:
                    callback()
                    logger.debug(f"[{self.name}] 清理回调执行成功")
                except Exception as e:
                    logger.error(f"[{self.name}] 清理回调执行失败: {str(e)}")
            
            # 清理注册的资源
            for resource, cleanup_func, description in reversed(self._resources):
                try:
                    if resource is not None:
                        cleanup_func(resource)
                        logger.debug(f"[{self.name}] 清理资源成功: {description}")
                except Exception as e:
                    logger.error(f"[{self.name}] 清理资源失败 [{description}]: {str(e)}")
            
            # 清空资源列表
            self._resources.clear()
            self._cleanup_callbacks.clear()
        
        logger.info(f"[{self.name}] 资源清理完成")
    
    def get_resource_count(self) -> int:
        """获取注册的资源数量"""
        with self._lock:
            return len(self._resources) + len(self._cleanup_callbacks)


class ThreadResourceManager(ResourceManager):
    """线程资源管理器"""
    
    def __init__(self):
        super().__init__("ThreadManager")
        self._threads: List[threading.Thread] = []
    
    def register_thread(self, thread: threading.Thread, timeout: float = 5.0) -> None:
        """注册线程资源"""
        self._threads.append(thread)
        self.register(thread, lambda t: self._cleanup_thread(t, timeout), f"Thread-{thread.name}")
    
    def _cleanup_thread(self, thread: threading.Thread, timeout: float) -> None:
        """清理线程资源"""
        if thread.is_alive():
            logger.debug(f"等待线程结束: {thread.name}")
            thread.join(timeout=timeout)
            if thread.is_alive():
                logger.warning(f"线程未在超时时间内结束: {thread.name}")


class AsyncResourceManager(ResourceManager):
    """异步资源管理器"""
    
    def __init__(self):
        super().__init__("AsyncManager")
        self._tasks: List[asyncio.Task] = []
        self._loops: List[asyncio.AbstractEventLoop] = []
    
    def register_task(self, task: asyncio.Task) -> None:
        """注册异步任务"""
        self._tasks.append(task)
        self.register(task, self._cleanup_task, f"Task-{task.get_name()}")
    
    def register_loop(self, loop: asyncio.AbstractEventLoop) -> None:
        """注册事件循环"""
        self._loops.append(loop)
        self.register(loop, self._cleanup_loop, "EventLoop")
    
    def _cleanup_task(self, task: asyncio.Task) -> None:
        """清理异步任务"""
        if not task.done():
            task.cancel()
            logger.debug(f"取消异步任务: {task.get_name()}")
    
    def _cleanup_loop(self, loop: asyncio.AbstractEventLoop) -> None:
        """清理事件循环"""
        if loop.is_running():
            logger.debug("停止事件循环")
            loop.call_soon_threadsafe(loop.stop)


class WebSocketResourceManager(ResourceManager):
    """WebSocket资源管理器"""
    
    def __init__(self):
        super().__init__("WebSocketManager")
        self._websockets: List[Any] = []  # websockets.WebSocketServerProtocol
    
    def register_websocket(self, websocket: Any) -> None:
        """注册WebSocket连接"""
        self._websockets.append(websocket)
        self.register(websocket, self._cleanup_websocket, "WebSocket")
    
    def _cleanup_websocket(self, websocket: Any) -> None:
        """清理WebSocket连接"""
        try:
            if hasattr(websocket, 'close'):
                if asyncio.iscoroutinefunction(websocket.close):
                    # 异步关闭
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        asyncio.create_task(websocket.close())
                    else:
                        loop.run_until_complete(websocket.close())
                else:
                    # 同步关闭
                    websocket.close()
                logger.debug("WebSocket连接已关闭")
        except Exception as e:
            logger.debug(f"关闭WebSocket连接时出错: {str(e)}")


class GlobalResourceManager:
    """全局资源管理器，管理所有子资源管理器"""
    
    def __init__(self):
        self.thread_manager = ThreadResourceManager()
        self.async_manager = AsyncResourceManager()
        self.websocket_manager = WebSocketResourceManager()
        self._custom_managers: List[ResourceManager] = []
        self._lock = threading.RLock()
    
    def add_custom_manager(self, manager: ResourceManager) -> None:
        """添加自定义资源管理器"""
        with self._lock:
            self._custom_managers.append(manager)
            logger.debug(f"添加自定义资源管理器: {manager.name}")
    
    def cleanup_all(self) -> None:
        """清理所有资源"""
        logger.info("开始全局资源清理...")
        
        # 清理自定义管理器
        for manager in self._custom_managers:
            try:
                manager.cleanup_all()
            except Exception as e:
                logger.error(f"清理自定义管理器失败 [{manager.name}]: {str(e)}")
        
        # 清理内置管理器
        try:
            self.websocket_manager.cleanup_all()
        except Exception as e:
            logger.error(f"清理WebSocket资源失败: {str(e)}")
        
        try:
            self.async_manager.cleanup_all()
        except Exception as e:
            logger.error(f"清理异步资源失败: {str(e)}")
        
        try:
            self.thread_manager.cleanup_all()
        except Exception as e:
            logger.error(f"清理线程资源失败: {str(e)}")
        
        # 清空自定义管理器列表
        with self._lock:
            self._custom_managers.clear()
        
        logger.info("全局资源清理完成")
    
    def get_status(self) -> dict:
        """获取资源管理器状态"""
        return {
            "thread_resources": self.thread_manager.get_resource_count(),
            "async_resources": self.async_manager.get_resource_count(),
            "websocket_resources": self.websocket_manager.get_resource_count(),
            "custom_managers": len(self._custom_managers)
        }


# 全局资源管理器实例
global_resource_manager = GlobalResourceManager()
