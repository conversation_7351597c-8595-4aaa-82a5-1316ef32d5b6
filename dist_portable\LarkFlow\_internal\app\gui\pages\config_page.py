"""
配置管理页面
分组显示配置项，支持敏感信息处理、配置验证和保存
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QCheckBox,
    QGroupBox, QScrollArea, QComboBox, QSpinBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from loguru import logger

from app.core.config_manager import config_manager


class ConfigPage(QWidget):
    """配置管理页面"""
    
    config_saved = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.config_widgets = {}
        self.init_ui()
        self.load_config()
        logger.info("配置管理页面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("配置管理")
        title_label.setObjectName("title")
        layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 创建配置内容
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        
        # 创建配置分组
        self.create_config_groups(config_layout)
        
        # 添加按钮区域
        self.create_button_area(config_layout)
        
        config_layout.addStretch()
        scroll_area.setWidget(config_widget)
        layout.addWidget(scroll_area)
    
    def create_config_groups(self, parent_layout):
        """创建配置分组"""
        schema = config_manager.get_config_schema()
        
        # 按类别分组
        categories = {}
        for key, config in schema.items():
            category = config.get('category', '其他配置')
            if category not in categories:
                categories[category] = []
            categories[category].append((key, config))
        
        # 创建各个分组
        for category, configs in categories.items():
            group_box = QGroupBox(category)
            group_layout = QGridLayout(group_box)
            
            row = 0
            for key, config in configs:
                self.create_config_item(group_layout, row, key, config)
                row += 1
            
            parent_layout.addWidget(group_box)
    
    def create_config_item(self, layout, row, key, config):
        """创建单个配置项"""
        # 标签
        label = QLabel(config.get('description', key))
        if config.get('required', False):
            label.setText(label.text() + " *")
            label.setStyleSheet("color: #dc3545; font-weight: 500;")
        
        layout.addWidget(label, row, 0)
        
        # 根据类型创建不同的输入控件
        config_type = config.get('type', 'text')
        
        if config_type == 'text':
            widget = QLineEdit()
            if key in ['LARK_COOKIE', 'LARK_APP_SECRET']:
                widget.setEchoMode(QLineEdit.EchoMode.Password)
        
        elif config_type == 'password':
            widget = QLineEdit()
            widget.setEchoMode(QLineEdit.EchoMode.Password)
        
        elif config_type == 'textarea':
            widget = QTextEdit()
            widget.setMaximumHeight(80)
        
        elif config_type == 'boolean':
            widget = QCheckBox()
        
        elif config_type == 'number':
            widget = QSpinBox()
            widget.setRange(0, 9999)
        
        elif config_type == 'select':
            widget = QComboBox()
            options = config.get('options', [])
            for option in options:
                widget.addItem(option['label'], option['value'])
        
        else:
            widget = QLineEdit()
        
        # 设置默认值
        default_value = config.get('default', '')
        if config_type == 'boolean':
            widget.setChecked(default_value.lower() == 'true' if isinstance(default_value, str) else bool(default_value))
        elif config_type == 'number':
            widget.setValue(int(default_value) if default_value else 0)
        elif config_type == 'select':
            index = widget.findData(default_value)
            if index >= 0:
                widget.setCurrentIndex(index)
        else:
            if hasattr(widget, 'setText'):
                widget.setText(str(default_value))
            elif hasattr(widget, 'setPlainText'):
                widget.setPlainText(str(default_value))
        
        layout.addWidget(widget, row, 1)
        
        # 保存控件引用
        self.config_widgets[key] = widget
        
        # 为密码字段添加显示/隐藏按钮
        if config_type in ['password'] or key in ['LARK_COOKIE', 'LARK_APP_SECRET']:
            toggle_btn = QPushButton("👁")
            toggle_btn.setFixedSize(30, 30)
            toggle_btn.clicked.connect(lambda checked, w=widget: self.toggle_password_visibility(w))
            layout.addWidget(toggle_btn, row, 2)
    
    def create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 保存配置按钮
        save_button = QPushButton("保存配置")
        save_button.setObjectName("success")
        save_button.clicked.connect(self.save_config)
        
        # 重置配置按钮
        reset_button = QPushButton("重置配置")
        reset_button.setObjectName("secondary")
        reset_button.clicked.connect(self.reset_config)
        
        # 验证配置按钮
        validate_button = QPushButton("验证配置")
        validate_button.clicked.connect(self.validate_config)
        
        button_layout.addWidget(save_button)
        button_layout.addWidget(reset_button)
        button_layout.addWidget(validate_button)
        button_layout.addStretch()
        
        parent_layout.addLayout(button_layout)
    
    def toggle_password_visibility(self, widget):
        """切换密码可见性"""
        if widget.echoMode() == QLineEdit.EchoMode.Password:
            widget.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            widget.setEchoMode(QLineEdit.EchoMode.Password)
    
    def load_config(self):
        """加载配置"""
        try:
            config_data = config_manager.get_all_config()
            
            for key, widget in self.config_widgets.items():
                value = config_data.get(key, '')
                
                if isinstance(widget, QCheckBox):
                    widget.setChecked(value.lower() == 'true' if isinstance(value, str) else bool(value))
                elif isinstance(widget, QSpinBox):
                    widget.setValue(int(value) if value else 0)
                elif isinstance(widget, QComboBox):
                    index = widget.findData(value)
                    if index >= 0:
                        widget.setCurrentIndex(index)
                elif isinstance(widget, QTextEdit):
                    widget.setPlainText(str(value))
                else:
                    widget.setText(str(value))
            
            logger.info("配置加载完成")
            
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            self.show_notification(f"加载配置失败: {str(e)}", "error")
    
    def save_config(self):
        """保存配置"""
        try:
            # 收集配置数据
            config_data = {}
            
            for key, widget in self.config_widgets.items():
                if isinstance(widget, QCheckBox):
                    config_data[key] = 'true' if widget.isChecked() else 'false'
                elif isinstance(widget, QSpinBox):
                    config_data[key] = str(widget.value())
                elif isinstance(widget, QComboBox):
                    config_data[key] = widget.currentData()
                elif isinstance(widget, QTextEdit):
                    config_data[key] = widget.toPlainText()
                else:
                    config_data[key] = widget.text()
            
            # 保存配置
            for key, value in config_data.items():
                config_manager.set_config(key, value)
            
            config_manager.save_config()
            
            # 重新加载应用配置
            try:
                from app.config.settings import settings
                settings.reload()
                logger.info("应用配置重新加载成功")
            except Exception as e:
                logger.warning(f"重新加载应用配置失败: {str(e)}")

            # 重新加载config_manager配置缓存
            try:
                config_manager.reload_config()
                logger.info("配置管理器重新加载成功")
            except Exception as e:
                logger.warning(f"重新加载配置管理器失败: {str(e)}")

            # 通知状态管理器同步配置开关
            try:
                from app.core.state_manager import state_manager
                state_manager.sync_config_switches()
                logger.info("配置开关同步成功")
            except Exception as e:
                logger.warning(f"同步配置开关失败: {str(e)}")

            self.show_notification("配置保存成功！配置已热重载，无需重启程序。", "success")
            self.config_saved.emit()
            logger.info("配置保存成功，已完成热重载")

        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            self.show_notification(f"保存配置失败: {str(e)}", "error")
    
    def reset_config(self):
        """重置配置"""
        # 简化重置逻辑，直接执行重置
        try:
            schema = config_manager.get_config_schema()

            for key, widget in self.config_widgets.items():
                config = schema.get(key, {})
                default_value = config.get('default', '')

                if isinstance(widget, QCheckBox):
                    widget.setChecked(default_value.lower() == 'true' if isinstance(default_value, str) else bool(default_value))
                elif isinstance(widget, QSpinBox):
                    widget.setValue(int(default_value) if default_value else 0)
                elif isinstance(widget, QComboBox):
                    index = widget.findData(default_value)
                    if index >= 0:
                        widget.setCurrentIndex(index)
                elif isinstance(widget, QTextEdit):
                    widget.setPlainText(str(default_value))
                else:
                    widget.setText(str(default_value))

            logger.info("配置重置完成")
            self.show_notification("配置已重置到默认值", "success")

        except Exception as e:
            logger.error(f"重置配置失败: {str(e)}")
            self.show_notification(f"重置配置失败: {str(e)}", "error")
    
    def validate_config(self):
        """验证配置"""
        try:
            # 这里可以添加配置验证逻辑
            # 例如检查必需字段、格式验证等
            
            errors = []
            schema = config_manager.get_config_schema()
            
            for key, widget in self.config_widgets.items():
                config = schema.get(key, {})
                
                if config.get('required', False):
                    value = ''
                    if isinstance(widget, QCheckBox):
                        value = 'true' if widget.isChecked() else 'false'
                    elif isinstance(widget, QTextEdit):
                        value = widget.toPlainText().strip()
                    else:
                        value = widget.text().strip()
                    
                    if not value:
                        errors.append(f"{config.get('description', key)} 是必需的")
            
            if errors:
                self.show_notification(f"验证失败: {'; '.join(errors)}", "error")
            else:
                self.show_notification("配置验证通过！", "success")

        except Exception as e:
            logger.error(f"验证配置失败: {str(e)}")
            self.show_notification(f"验证配置失败: {str(e)}", "error")
    
    def refresh_data(self):
        """刷新页面数据"""
        self.load_config()

    def show_notification(self, message: str, notification_type: str = "info"):
        """显示通知消息"""
        try:
            # 创建临时标签显示通知
            if hasattr(self, 'notification_label'):
                self.notification_label.deleteLater()

            self.notification_label = QLabel(message)
            self.notification_label.setObjectName(f"notification-{notification_type}")

            # 设置通知样式
            if notification_type == "success":
                self.notification_label.setStyleSheet("""
                    QLabel#notification-success {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                        border-radius: 4px;
                        padding: 8px 12px;
                        margin: 5px 0;
                    }
                """)
            elif notification_type == "error":
                self.notification_label.setStyleSheet("""
                    QLabel#notification-error {
                        background-color: #f8d7da;
                        color: #721c24;
                        border: 1px solid #f5c6cb;
                        border-radius: 4px;
                        padding: 8px 12px;
                        margin: 5px 0;
                    }
                """)
            else:  # info
                self.notification_label.setStyleSheet("""
                    QLabel#notification-info {
                        background-color: #d1ecf1;
                        color: #0c5460;
                        border: 1px solid #bee5eb;
                        border-radius: 4px;
                        padding: 8px 12px;
                        margin: 5px 0;
                    }
                """)

            # 添加到布局顶部
            layout = self.layout()
            layout.insertWidget(1, self.notification_label)

            # 3秒后自动隐藏
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(3000, self.hide_notification)

        except Exception as e:
            logger.error(f"显示通知失败: {str(e)}")

    def hide_notification(self):
        """隐藏通知"""
        try:
            if hasattr(self, 'notification_label'):
                self.notification_label.deleteLater()
                delattr(self, 'notification_label')
        except Exception as e:
            logger.error(f"隐藏通知失败: {str(e)}")
