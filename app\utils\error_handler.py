"""
用户友好的错误处理器
提供清晰的错误信息和解决建议
"""
import functools
import traceback
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from loguru import logger


class UserFriendlyErrorHandler:
    """用户友好的错误处理器"""
    
    # 错误类型映射
    ERROR_MESSAGES = {
        "websocket_connection_failed": {
            "title": "连接失败",
            "message": "无法连接到飞书服务器，请检查：\n1. 网络连接是否正常\n2. 飞书Cookie是否有效\n3. 是否需要更新Cookie",
            "actions": ["检查网络", "更新Cookie", "重试连接"],
            "severity": "error"
        },
        "music_file_not_found": {
            "title": "音乐文件未找到",
            "message": "找不到音乐文件，请：\n1. 检查文件路径是否正确\n2. 确认文件是否存在\n3. 选择其他音乐文件",
            "actions": ["选择文件", "使用默认", "跳过音乐"],
            "severity": "warning"
        },
        "config_save_failed": {
            "title": "配置保存失败",
            "message": "无法保存配置文件，请检查：\n1. 文件权限是否正确\n2. 磁盘空间是否充足\n3. 配置格式是否正确",
            "actions": ["检查权限", "重试保存", "恢复默认"],
            "severity": "error"
        },
        "phone_notification_failed": {
            "title": "电话提醒失败",
            "message": "无法发送电话提醒，请检查：\n1. 电话号码是否正确\n2. 网络连接是否正常\n3. 服务是否可用",
            "actions": ["检查号码", "重试发送", "使用其他方式"],
            "severity": "warning"
        },
        "message_processing_failed": {
            "title": "消息处理失败",
            "message": "处理飞书消息时出错，可能原因：\n1. 消息格式不支持\n2. 网络连接中断\n3. 服务器响应异常",
            "actions": ["重试处理", "跳过消息", "检查日志"],
            "severity": "warning"
        }
    }
    
    @classmethod
    def get_friendly_error(cls, error_type: str, original_error: str = "", context: Dict[str, Any] = None) -> Dict[str, Any]:
        """获取用户友好的错误信息"""
        base_error = cls.ERROR_MESSAGES.get(error_type, {
            "title": "未知错误",
            "message": f"发生了未知错误：{original_error}",
            "actions": ["重试", "查看日志", "联系支持"],
            "severity": "error"
        })
        
        # 添加上下文信息
        if context:
            message = base_error["message"]
            if "file_path" in context:
                message += f"\n\n文件路径: {context['file_path']}"
            if "error_code" in context:
                message += f"\n错误代码: {context['error_code']}"
            base_error["message"] = message
        
        # 添加原始错误信息（用于调试）
        base_error["original_error"] = original_error
        base_error["context"] = context or {}
        
        return base_error
    
    @classmethod
    def format_error_for_display(cls, error_info: Dict[str, Any]) -> str:
        """格式化错误信息用于显示"""
        title = error_info.get("title", "错误")
        message = error_info.get("message", "发生了未知错误")
        actions = error_info.get("actions", [])
        
        formatted = f"{title}\n\n{message}"
        
        if actions:
            formatted += f"\n\n建议操作：\n"
            for i, action in enumerate(actions, 1):
                formatted += f"{i}. {action}\n"
        
        return formatted


def handle_exceptions(error_type: str = None, show_dialog: bool = True, log_error: bool = True):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 获取错误信息
                original_error = str(e)
                error_traceback = traceback.format_exc()
                
                # 确定错误类型
                actual_error_type = error_type or _determine_error_type(e)
                
                # 获取友好的错误信息
                error_info = UserFriendlyErrorHandler.get_friendly_error(
                    actual_error_type, 
                    original_error,
                    {"function": func.__name__}
                )
                
                # 记录错误日志
                if log_error:
                    logger.error(f"函数 {func.__name__} 执行失败: {original_error}")
                    logger.debug(f"错误堆栈: {error_traceback}")
                
                # 显示错误对话框
                if show_dialog and hasattr(args[0], 'show_error_message'):
                    formatted_error = UserFriendlyErrorHandler.format_error_for_display(error_info)
                    args[0].show_error_message(formatted_error)
                
                # 更新状态管理器
                try:
                    from app.core.state_manager import state_manager
                    state_manager.set_error(error_info["title"])
                except:
                    pass
                
                return None
        return wrapper
    return decorator


def _determine_error_type(exception: Exception) -> str:
    """根据异常类型确定错误类型"""
    exception_name = type(exception).__name__
    error_message = str(exception).lower()
    
    # 根据异常类型和消息内容判断错误类型
    if "connection" in error_message or "network" in error_message:
        return "websocket_connection_failed"
    elif "file not found" in error_message or "no such file" in error_message:
        return "music_file_not_found"
    elif "permission" in error_message or "access denied" in error_message:
        return "config_save_failed"
    elif "phone" in error_message or "call" in error_message:
        return "phone_notification_failed"
    elif "message" in error_message:
        return "message_processing_failed"
    else:
        return "unknown_error"


class ErrorReporter:
    """错误报告器，收集和分析错误模式"""
    
    def __init__(self):
        self._error_history = []
        self._error_counts = {}
        self._lock = threading.Lock()
    
    def report_error(self, error_type: str, error_message: str, context: Dict[str, Any] = None):
        """报告错误"""
        import threading
        
        with self._lock:
            error_record = {
                "type": error_type,
                "message": error_message,
                "context": context or {},
                "timestamp": time.time(),
                "thread": threading.current_thread().name
            }
            
            self._error_history.append(error_record)
            self._error_counts[error_type] = self._error_counts.get(error_type, 0) + 1
            
            # 限制历史记录大小
            if len(self._error_history) > 1000:
                self._error_history = self._error_history[-500:]
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        with self._lock:
            return {
                "total_errors": len(self._error_history),
                "error_counts": self._error_counts.copy(),
                "recent_errors": self._error_history[-10:] if self._error_history else []
            }
    
    def get_frequent_errors(self, threshold: int = 5) -> List[str]:
        """获取频繁出现的错误类型"""
        with self._lock:
            return [error_type for error_type, count in self._error_counts.items() if count >= threshold]


# 全局错误报告器实例
error_reporter = ErrorReporter()


# 便捷的错误处理装饰器
def safe_execute(error_type: str = None):
    """安全执行装饰器，捕获异常但不显示对话框"""
    return handle_exceptions(error_type=error_type, show_dialog=False, log_error=True)


def user_friendly_execute(error_type: str = None):
    """用户友好执行装饰器，显示友好的错误对话框"""
    return handle_exceptions(error_type=error_type, show_dialog=True, log_error=True)
