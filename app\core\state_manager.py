"""
状态管理器
用于管理应用程序的运行状态和开关控制
（已从app/web/移动到app/core/，供GUI和核心功能共享使用）
"""
import threading
import time
import collections
from typing import Dict, Any, Optional, Callable
from loguru import logger

from app.utils.error_handler import safe_execute


class StateManager:
    """应用状态管理器"""
    
    def __init__(self):
        self._lock = threading.RLock()

        # 初始化事件发射器（延迟导入避免循环依赖）
        self._event_emitter = None

        # 状态历史记录（限制大小防止内存泄漏）
        self._state_history = collections.deque(maxlen=100)

        # 清理定时器
        self._cleanup_timer = None
        self._cleanup_interval = 300  # 5分钟清理一次

        # 从配置文件同步初始开关状态
        from app.config.settings import settings

        self._state = {
            # 运行状态
            "is_running": False,
            "start_time": 0,  # 初始化为0而不是None
            "websocket_connected": False,
            "last_message_time": 0,  # 初始化为0而不是None
            "main_app_running": False,  # 主应用运行状态

            # 功能开关 - 从配置文件同步初始状态
            "message_listening_enabled": True,
            "auto_reply_enabled": True,
            "music_notification_enabled": settings.ENABLE_MUSIC_NOTIFICATION,
            "phone_notification_enabled": settings.ENABLE_PHONE_NOTIFICATION,

            # 统计信息
            "total_messages_received": 0,
            "total_replies_sent": 0,
            "total_music_notifications": 0,
            "total_phone_notifications": 0,

            # 错误信息
            "last_error": None,
            "error_count": 0,

            # 音乐播放事件
            "music_play_event": None,
            "last_music_event_time": 0,  # 初始化为0而不是None

            # 弹窗事件
            "dialog_event": None,
            "last_dialog_event_time": 0,  # 初始化为0而不是None

            # 连接质量监控
            "connection_quality": "unknown",  # unknown, good, poor, disconnected
            "last_heartbeat_time": 0,  # 初始化为0而不是None
            "reconnect_count": 0,
            "connection_uptime": 0
        }

        # 状态变化回调
        self._callbacks = {}

        logger.info(f"状态管理器初始化完成 - 音乐提醒: {self._state['music_notification_enabled']}, 电话提醒: {self._state['phone_notification_enabled']}")

        # 启动清理定时器
        self._start_cleanup_timer()

    def _start_cleanup_timer(self):
        """启动状态清理定时器"""
        if self._cleanup_timer is None:
            self._cleanup_timer = threading.Timer(self._cleanup_interval, self._cleanup_old_states)
            self._cleanup_timer.daemon = True
            self._cleanup_timer.start()
            logger.debug("状态清理定时器已启动")

    @safe_execute("state_cleanup_failed")
    def _cleanup_old_states(self):
        """清理过期状态"""
        current_time = time.time()
        cutoff_time = current_time - 3600  # 1小时前

        with self._lock:
            # 清理过期的事件
            last_music_time = self._state.get("last_music_event_time", 0)
            if last_music_time > 0 and last_music_time < cutoff_time:
                self._state["music_play_event"] = None
                logger.debug("清理过期的音乐播放事件")

            last_dialog_time = self._state.get("last_dialog_event_time", 0)
            if last_dialog_time > 0 and last_dialog_time < cutoff_time:
                self._state["dialog_event"] = None
                logger.debug("清理过期的对话框事件")

            # 清理过期的错误信息（保留最近的错误）
            if self._state.get("last_error") and self._state.get("error_count", 0) > 10:
                # 如果错误数量过多，重置计数器
                self._state["error_count"] = 1
                logger.debug("重置错误计数器")

        logger.debug("状态清理完成")

        # 重新启动定时器
        self._cleanup_timer = None
        self._start_cleanup_timer()

    def _get_event_emitter(self):
        """获取事件发射器（延迟初始化）"""
        if self._event_emitter is None:
            try:
                from app.utils.event_emitter import state_event_emitter
                self._event_emitter = state_event_emitter
            except ImportError:
                logger.warning("事件发射器不可用，将使用传统回调机制")
                self._event_emitter = None
        return self._event_emitter

    def get_state(self, key: str = None) -> Any:
        """获取状态值"""
        with self._lock:
            if key is None:
                return self._state.copy()
            return self._state.get(key)
    
    def set_state(self, key: str, value: Any, notify: bool = True) -> None:
        """设置状态值"""
        with self._lock:
            old_value = self._state.get(key)
            self._state[key] = value

            if notify and old_value != value:
                self._notify_callbacks(key, old_value, value)

                # 发射事件
                event_emitter = self._get_event_emitter()
                if event_emitter:
                    event_emitter.emit_state_change(key, old_value, value)

                # 发射事件
                event_emitter = self._get_event_emitter()
                if event_emitter:
                    event_emitter.emit_state_change(key, old_value, value)
    
    def update_state(self, updates: Dict[str, Any], notify: bool = True) -> None:
        """批量更新状态"""
        with self._lock:
            changes = {}
            for key, value in updates.items():
                old_value = self._state.get(key)
                if old_value != value:
                    changes[key] = (old_value, value)
                    self._state[key] = value
            
            if notify and changes:
                for key, (old_value, new_value) in changes.items():
                    self._notify_callbacks(key, old_value, new_value)
    
    def register_callback(self, key: str, callback: Callable[[str, Any, Any], None]) -> None:
        """注册状态变化回调"""
        with self._lock:
            if key not in self._callbacks:
                self._callbacks[key] = []
            self._callbacks[key].append(callback)
    
    def _notify_callbacks(self, key: str, old_value: Any, new_value: Any) -> None:
        """通知状态变化回调"""
        callbacks = self._callbacks.get(key, [])
        for callback in callbacks:
            try:
                callback(key, old_value, new_value)
            except Exception as e:
                logger.error(f"状态变化回调执行失败: {str(e)}")
    
    # 应用控制方法
    def start_application(self) -> None:
        """启动应用"""
        with self._lock:
            self._state["is_running"] = True
            self._state["start_time"] = time.time()
            self._state["last_error"] = None
        logger.info("应用状态设置为启动")
    
    def stop_application(self) -> None:
        """停止应用"""
        with self._lock:
            self._state["is_running"] = False
            self._state["start_time"] = None
            self._state["websocket_connected"] = False
            self._state["main_app_running"] = False
        logger.info("应用状态设置为停止")
    
    def set_main_app_running(self, running: bool) -> None:
        """设置主应用运行状态"""
        with self._lock:
            self._state["main_app_running"] = running
        logger.info(f"主应用运行状态: {running}")
    
    # WebSocket连接管理
    def set_websocket_connected(self, connected: bool) -> None:
        """设置WebSocket连接状态"""
        with self._lock:
            old_connected = self._state["websocket_connected"]
            self._state["websocket_connected"] = connected

            if connected and not old_connected:
                self._state["connection_uptime"] = time.time()
                self._state["connection_quality"] = "good"
                logger.info("WebSocket连接已建立")
            elif not connected and old_connected:
                self._state["connection_quality"] = "disconnected"
                logger.info("WebSocket连接已断开")

            # 发射WebSocket连接事件
            if old_connected != connected:
                event_emitter = self._get_event_emitter()
                if event_emitter:
                    event_emitter.emit_websocket_event(connected)
    
    def update_heartbeat(self) -> None:
        """更新心跳时间"""
        with self._lock:
            self._state["last_heartbeat_time"] = time.time()
            if self._state["websocket_connected"]:
                self._state["connection_quality"] = "good"

    def check_connection_quality(self) -> None:
        """检查连接质量"""
        with self._lock:
            if not self._state["websocket_connected"]:
                self._state["connection_quality"] = "disconnected"
                return

            current_time = time.time()
            last_heartbeat = self._state.get("last_heartbeat_time")
            last_message = self._state.get("last_message_time")

            # 如果没有心跳记录，使用连接建立时间
            if last_heartbeat is None:
                last_heartbeat = self._state.get("connection_uptime", current_time)

            # 检查心跳间隔
            heartbeat_interval = current_time - last_heartbeat

            # 检查消息间隔
            message_interval = float('inf')
            if last_message:
                message_interval = current_time - last_message

            # 根据间隔判断连接质量
            if heartbeat_interval > 600:  # 10分钟无心跳
                self._state["connection_quality"] = "poor"
            elif heartbeat_interval > 1800:  # 30分钟无心跳
                self._state["connection_quality"] = "disconnected"
            elif message_interval < 3600:  # 1小时内有消息
                self._state["connection_quality"] = "good"
            else:
                self._state["connection_quality"] = "good"
    
    def increment_reconnect_count(self) -> None:
        """增加重连次数"""
        with self._lock:
            self._state["reconnect_count"] += 1
        logger.info(f"WebSocket重连次数: {self._state['reconnect_count']}")
    
    def reset_reconnect_count(self) -> None:
        """重置重连次数"""
        with self._lock:
            self._state["reconnect_count"] = 0
    
    # 统计信息管理
    def increment_message_count(self) -> None:
        """增加消息计数"""
        with self._lock:
            self._state["total_messages_received"] += 1
            self._state["last_message_time"] = time.time()
    
    def increment_reply_count(self) -> None:
        """增加回复计数"""
        with self._lock:
            self._state["total_replies_sent"] += 1
    
    def increment_music_notification_count(self) -> None:
        """增加音乐提醒计数"""
        with self._lock:
            self._state["total_music_notifications"] += 1
    
    def increment_phone_notification_count(self) -> None:
        """增加电话提醒计数"""
        with self._lock:
            self._state["total_phone_notifications"] += 1
    
    # 错误管理
    def set_error(self, error_message: str) -> None:
        """设置错误信息"""
        with self._lock:
            self._state["last_error"] = error_message
            self._state["error_count"] += 1
        logger.error(f"应用错误: {error_message}")

        # 发射错误事件
        event_emitter = self._get_event_emitter()
        if event_emitter:
            event_emitter.emit_error_event(error_message)
    
    def clear_error(self) -> None:
        """清除错误信息"""
        with self._lock:
            self._state["last_error"] = None
    
    # 功能开关管理
    def toggle_message_listening(self) -> bool:
        """切换消息监听开关"""
        with self._lock:
            current = self._state["message_listening_enabled"]
            self._state["message_listening_enabled"] = not current
            new_state = self._state["message_listening_enabled"]
        logger.info(f"消息监听开关: {new_state}")
        return new_state
    
    def toggle_auto_reply(self) -> bool:
        """切换自动回复开关"""
        with self._lock:
            current = self._state["auto_reply_enabled"]
            self._state["auto_reply_enabled"] = not current
            new_state = self._state["auto_reply_enabled"]
        logger.info(f"自动回复开关: {new_state}")
        return new_state
    
    def toggle_music_notification(self) -> bool:
        """切换音乐提醒开关"""
        with self._lock:
            current = self._state["music_notification_enabled"]
            self._state["music_notification_enabled"] = not current
            new_state = self._state["music_notification_enabled"]
        logger.info(f"音乐提醒开关: {new_state}")
        return new_state
    
    def toggle_phone_notification(self) -> bool:
        """切换电话提醒开关"""
        with self._lock:
            current = self._state["phone_notification_enabled"]
            self._state["phone_notification_enabled"] = not current
            new_state = self._state["phone_notification_enabled"]
        logger.info(f"电话提醒开关: {new_state}")
        return new_state

    # 音乐播放事件管理
    def set_music_play_event(self, event_data: dict) -> None:
        """设置音乐播放事件"""
        with self._lock:
            self._state["music_play_event"] = event_data
            self._state["last_music_event_time"] = time.time()

    def clear_music_play_event(self) -> None:
        """清除音乐播放事件"""
        with self._lock:
            self._state["music_play_event"] = None

    def set_dialog_event(self, event_data: dict) -> None:
        """设置弹窗事件"""
        with self._lock:
            self._state["dialog_event"] = event_data
            self._state["last_dialog_event_time"] = time.time()

    def clear_dialog_event(self) -> None:
        """清除弹窗事件"""
        with self._lock:
            self._state["dialog_event"] = None

    # 状态摘要
    def get_status_summary(self) -> dict:
        """获取状态摘要"""
        # 检查连接质量
        self.check_connection_quality()

        with self._lock:
            state = self._state.copy()

        # 计算运行时间
        uptime = None
        if state.get("start_time"):
            uptime = time.time() - state["start_time"]

        return {
            "running": state.get("is_running", False),
            "main_app_running": state.get("main_app_running", False),
            "uptime": uptime,
            "uptime_formatted": self._format_uptime(uptime) if uptime else "未运行",
            "websocket_connected": state.get("websocket_connected", False),
            "connection_quality": state.get("connection_quality", "unknown"),
            "last_heartbeat_time": state.get("last_heartbeat_time"),
            "reconnect_count": state.get("reconnect_count", 0),
            "connection_uptime": state.get("connection_uptime", 0),
            "message_listening_enabled": state.get("message_listening_enabled", True),
            "auto_reply_enabled": state.get("auto_reply_enabled", True),
            "music_notification_enabled": state.get("music_notification_enabled", True),
            "phone_notification_enabled": state.get("phone_notification_enabled", True),
            "total_messages": state.get("total_messages_received", 0),
            "total_replies": state.get("total_replies_sent", 0),
            "total_music_notifications": state.get("total_music_notifications", 0),
            "total_phone_notifications": state.get("total_phone_notifications", 0),
            "last_error": state.get("last_error"),
            "error_count": state.get("error_count", 0),
            "last_message_time": state.get("last_message_time"),
            "music_play_event": state.get("music_play_event"),
            "last_music_event_time": state.get("last_music_event_time"),
            "dialog_event": state.get("dialog_event"),
            "last_dialog_event_time": state.get("last_dialog_event_time")
        }

    def _format_uptime(self, uptime: float) -> str:
        """格式化运行时间"""
        if uptime < 60:
            return f"{int(uptime)}秒"
        elif uptime < 3600:
            minutes = int(uptime // 60)
            seconds = int(uptime % 60)
            return f"{minutes}分{seconds}秒"
        else:
            hours = int(uptime // 3600)
            minutes = int((uptime % 3600) // 60)
            return f"{hours}小时{minutes}分"

    def sync_config_switches(self) -> None:
        """同步配置文件的开关状态"""
        try:
            from app.config.settings import settings

            # 重新加载配置并同步开关状态
            config_music_enabled = settings.ENABLE_MUSIC_NOTIFICATION
            config_phone_enabled = settings.ENABLE_PHONE_NOTIFICATION

            # 只有当配置文件禁用时才强制禁用GUI开关
            if not config_music_enabled:
                self.set_state("music_notification_enabled", False, notify=False)
                logger.info("音乐提醒开关已同步为禁用（配置文件设置）")

            if not config_phone_enabled:
                self.set_state("phone_notification_enabled", False, notify=False)
                logger.info("电话提醒开关已同步为禁用（配置文件设置）")

        except Exception as e:
            logger.error(f"同步配置开关失败: {str(e)}")

    # 应用控制方法
    def is_main_application_running(self) -> bool:
        """检查主应用是否正在运行"""
        try:
            from app.core.app_service_manager import app_service_manager
            return app_service_manager.is_running
        except Exception as e:
            logger.error(f"检查主应用运行状态失败: {str(e)}")
            return False

    def start_main_application(self) -> bool:
        """启动主应用"""
        try:
            from app.core.app_service_manager import app_service_manager

            # 设置错误回调，包含状态更新
            def error_callback_with_state_update(error_msg):
                self.set_error(error_msg)
                # 确保在错误时更新应用运行状态
                self.set_state("main_app_running", False)
                logger.error(f"主应用错误，状态已更新: {error_msg}")

            app_service_manager.set_error_callback(error_callback_with_state_update)

            # 启动应用
            success = app_service_manager.start_application()
            if success:
                self.set_state("main_app_running", True)
                logger.info("主应用启动成功")
            else:
                self.set_state("main_app_running", False)
                logger.error("主应用启动失败")

            return success

        except Exception as e:
            error_msg = f"启动主应用异常: {str(e)}"
            logger.error(error_msg)
            self.set_error(error_msg)
            self.set_state("main_app_running", False)
            return False

    def stop_main_application(self) -> bool:
        """停止主应用"""
        try:
            from app.core.app_service_manager import app_service_manager

            success = app_service_manager.stop_application()
            if success:
                self.set_state("main_app_running", False)
                self.set_state("websocket_connected", False)
                logger.info("主应用停止成功")
            else:
                logger.error("主应用停止失败")

            return success

        except Exception as e:
            error_msg = f"停止主应用异常: {str(e)}"
            logger.error(error_msg)
            self.set_error(error_msg)
            return False

    def get_main_app_status(self) -> dict:
        """获取主应用状态"""
        try:
            from app.core.app_service_manager import app_service_manager
            status = app_service_manager.get_status()

            # 同步状态到状态管理器
            current_state = self.get_state("main_app_running")
            actual_running = status.get("is_running", False)

            if current_state != actual_running:
                logger.info(f"检测到主应用状态不一致，更新状态: {current_state} -> {actual_running}")
                self.set_state("main_app_running", actual_running, notify=False)

                # 如果应用已停止，也要更新WebSocket状态
                if not actual_running:
                    self.set_state("websocket_connected", False, notify=False)

            return status
        except Exception as e:
            logger.error(f"获取主应用状态失败: {str(e)}")
            # 发生异常时，假设应用已停止
            self.set_state("main_app_running", False, notify=False)
            self.set_state("websocket_connected", False, notify=False)
            return {"is_running": False}


# 全局状态管理器实例
state_manager = StateManager()
