#!/usr/bin/env python3
"""
LarkFlow GUI启动器
基于PyQt6的现代化桌面界面
"""
import sys
import os
import signal
from pathlib import Path
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.logger_config import setup_unified_logging
from loguru import logger

# 立即设置统一日志配置，确保在所有其他操作之前完成
setup_unified_logging("LarkFlow GUI")

def check_environment():
    """检查运行环境"""
    logger.info("检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        logger.error("Python版本过低，需要Python 3.8或更高版本")
        return False
    
    # 检查必要的模块
    required_modules = ['PyQt6', 'loguru', 'requests', 'websockets']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"缺少必要的模块: {', '.join(missing_modules)}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    # 检查配置文件
    if not os.path.exists('.env'):
        logger.warning("未找到.env配置文件，将使用默认配置")
        logger.info("建议复制.env.example为.env并配置相关参数")
    
    logger.info("环境检查通过")
    return True

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在退出...")
    sys.exit(0)

def main():
    """主函数"""
    print("LarkFlow GUI 启动器")
    print("=" * 50)
    
    # 日志配置已在文件开头完成
    
    # 检查环境
    if not check_environment():
        input("按回车键退出...")
        sys.exit(1)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 导入PyQt6
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QIcon
        
        # 导入GUI组件
        from app.gui.main_window import MainWindow
        from app.gui.styles.style_manager import style_manager
        
        logger.info("初始化GUI应用...")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("LarkFlow")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("LarkFlow Team")
        
        # 设置应用程序图标
        try:
            icon_path = Path("feishu.ico")
            if icon_path.exists():
                app.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            logger.warning(f"设置应用图标失败: {str(e)}")
        
        # 应用样式表
        style_manager.apply_style(app, "modern")
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 显示主窗口
        main_window.show()
        
        # 显示启动信息
        logger.info("GUI应用启动成功")
        print("\n" + "=" * 50)
        print("LarkFlow GUI 已启动")
        print("=" * 50)
        print("\n使用说明:")
        print("1. 在配置管理页面设置飞书Cookie等必要参数")
        print("2. 在状态概览页面启动主应用功能")
        print("3. 使用功能开关控制各项功能")
        print("4. 在日志查看页面监控运行状态")
        print("5. 在音频管理页面管理提醒音频")
        print("\n注意: 程序可以最小化到系统托盘运行")
        print("=" * 50)
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        logger.error(f"导入GUI模块失败: {str(e)}")
        logger.error("请确保已安装PyQt6: pip install PyQt6")
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        logger.error(f"GUI启动失败: {str(e)}")
        
        # 尝试显示错误对话框
        try:
            from PyQt6.QtWidgets import QApplication, QMessageBox
            if not QApplication.instance():
                app = QApplication(sys.argv)
            
            QMessageBox.critical(
                None,
                "启动失败",
                f"LarkFlow GUI启动失败:\n\n{str(e)}\n\n请检查日志文件获取详细信息。"
            )
        except:
            pass
        
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
