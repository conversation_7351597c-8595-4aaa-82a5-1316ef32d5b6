"""
样式管理器
负责加载和管理GUI样式表
"""
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from loguru import logger


class StyleManager:
    """样式管理器类"""
    
    def __init__(self):
        self.current_theme = "modern"
        self.styles_dir = Path(__file__).parent
    
    def load_style(self, theme_name: str = "modern") -> str:
        """加载指定主题的样式表"""
        try:
            style_file = self.styles_dir / f"{theme_name}_style.qss"
            
            if not style_file.exists():
                logger.warning(f"样式文件不存在: {style_file}")
                return ""
            
            with open(style_file, 'r', encoding='utf-8') as f:
                style_content = f.read()
            
            logger.info(f"成功加载样式主题: {theme_name}")
            self.current_theme = theme_name
            return style_content
            
        except Exception as e:
            logger.error(f"加载样式表失败: {str(e)}")
            return ""
    
    def apply_style(self, app: QApplication, theme_name: str = "modern"):
        """应用样式表到应用程序"""
        try:
            style_content = self.load_style(theme_name)
            if style_content:
                app.setStyleSheet(style_content)
                logger.info(f"样式表应用成功: {theme_name}")
            else:
                logger.warning("样式表内容为空，使用默认样式")
        except Exception as e:
            logger.error(f"应用样式表失败: {str(e)}")
    
    def get_available_themes(self) -> list:
        """获取可用的主题列表"""
        themes = []
        try:
            for file in self.styles_dir.glob("*_style.qss"):
                theme_name = file.stem.replace("_style", "")
                themes.append(theme_name)
        except Exception as e:
            logger.error(f"获取主题列表失败: {str(e)}")
        
        return themes


# 全局样式管理器实例
style_manager = StyleManager()
