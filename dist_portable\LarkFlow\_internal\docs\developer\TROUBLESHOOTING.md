# 故障排除指南

## 🔧 常见问题解决方案

### 1. Web开关状态不同步

**问题描述**：
- Web界面显示开关已开启
- 但日志显示功能已禁用
- 实际功能不工作

**原因分析**：
Web界面的状态管理和配置文件设置存在不一致

**解决方案**：

1. **检查配置文件**
   ```bash
   # 查看.env文件中的设置
   cat .env | grep ENABLE_
   ```

2. **重启应用程序**
   ```bash
   # 停止程序（Ctrl+C）
   # 重新启动
   python main.py
   ```

3. **通过Web界面重新设置**
   - 访问 http://127.0.0.1:8080/config
   - 检查相关配置项
   - 保存配置后重启

4. **检查日志信息**
   - 在Web界面查看详细日志
   - 搜索相关错误信息

### 2. Web界面无法访问

**问题描述**：
- 浏览器无法打开 http://127.0.0.1:8080
- 显示连接被拒绝

**解决方案**：

1. **检查端口占用**
   ```bash
   netstat -an | findstr 8080
   ```

2. **检查防火墙设置**
   - 确保8080端口未被阻止

3. **查看启动日志**
   - 检查是否有Web服务器启动失败的错误信息

4. **尝试其他端口**
   - 修改.env文件中的WEB_PORT设置

### 3. 配置保存失败

**问题描述**：
- 在Web界面修改配置后点击保存
- 显示保存失败或配置未生效

**解决方案**：

1. **检查文件权限**
   ```bash
   # 确保.env文件可写
   ls -la .env
   ```

2. **检查配置格式**
   - 确保配置值格式正确
   - 布尔值使用 true/false
   - 字符串使用引号

3. **手动编辑配置**
   ```bash
   # 直接编辑.env文件
   notepad .env
   ```

### 4. 音乐提醒不工作

**问题描述**：
- 开关已开启但音乐不播放
- 没有弹窗提醒

**解决方案**：

1. **检查音乐文件**
   ```bash
   # 确保音乐文件存在
   ls static/audio/
   ```

2. **检查pygame安装**
   ```bash
   pip install pygame
   ```

3. **检查音频设备**
   - 确保系统音频正常
   - 检查音量设置

4. **查看详细日志**
   - 在日志页面搜索"音乐"相关信息

### 5. 电话提醒不工作

**问题描述**：
- 开关已开启但电话提醒未发送
- 日志显示配置不完整

**解决方案**：

1. **检查飞书应用配置**
   ```env
   LARK_APP_ID="your_app_id"
   LARK_APP_SECRET="your_app_secret"
   ```

2. **检查用户ID和消息ID**
   ```env
   PHONE_NOTIFICATION_USER_IDS="user1,user2"
   PHONE_NOTIFICATION_MESSAGE_IDS="msg1,msg2"
   ```

3. **测试API连接**
   - 检查网络连接
   - 验证应用权限

### 6. WebSocket连接失败

**问题描述**：
- Web界面显示WebSocket未连接
- 无法接收消息

**解决方案**：

1. **检查飞书Cookie**
   ```env
   LARK_COOKIE="your_cookie_here"
   ```

2. **更新Cookie**
   - 重新获取飞书Cookie
   - 在配置页面更新

3. **检查网络连接**
   - 确保能访问飞书服务器
   - 检查代理设置

## 🛠️ 调试工具

### 1. 日志查看
- Web界面：http://127.0.0.1:8080/logs
- 控制台：直接查看程序输出

### 2. 配置验证
- Web界面：http://127.0.0.1:8080/config
- 命令行：检查.env文件内容

## 📞 获取帮助

如果以上方案都无法解决问题：

1. **查看详细日志**
   - 记录完整的错误信息
   - 包含时间戳和上下文

2. **提供环境信息**
   - Python版本
   - 操作系统版本
   - 依赖包版本

3. **提交Issue**
   - 在GitHub仓库提交详细的问题报告
   - 包含复现步骤和错误日志

4. **社区支持**
   - 查看项目Wiki
   - 参与社区讨论
