"""
线程安全的单例模式实现
提供多种单例模式的实现方式
"""
import threading
import weakref
from typing import Dict, Type, Any, Optional
from loguru import logger


class ThreadSafeSingleton(type):
    """线程安全的单例元类"""
    
    _instances: Dict[Type, Any] = {}
    _lock = threading.RLock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                # 双重检查锁定模式
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
                    logger.debug(f"创建单例实例: {cls.__name__}")
        return cls._instances[cls]
    
    @classmethod
    def clear_instances(mcs):
        """清除所有单例实例（用于测试或重置）"""
        with mcs._lock:
            mcs._instances.clear()
            logger.debug("清除所有单例实例")
    
    @classmethod
    def get_instance_count(mcs) -> int:
        """获取单例实例数量"""
        with mcs._lock:
            return len(mcs._instances)


class WeakSingleton(type):
    """使用弱引用的单例模式，允许垃圾回收"""
    
    _instances: Dict[Type, Any] = {}
    _lock = threading.RLock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances or cls._instances[cls]() is None:
            with cls._lock:
                # 双重检查锁定模式
                if cls not in cls._instances or cls._instances[cls]() is None:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = weakref.ref(instance)
                    logger.debug(f"创建弱引用单例实例: {cls.__name__}")
        
        return cls._instances[cls]()


class SingletonMixin:
    """单例混入类，提供单例功能"""
    
    _instances = {}
    _lock = threading.RLock()
    
    def __new__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__new__(cls)
                    cls._instances[cls] = instance
                    logger.debug(f"创建混入单例实例: {cls.__name__}")
        return cls._instances[cls]


class NamedSingleton:
    """命名单例，支持按名称创建多个实例"""
    
    _instances: Dict[str, Dict[Type, Any]] = {}
    _lock = threading.RLock()
    
    def __init__(self, name: str = "default"):
        self.name = name
    
    def __new__(cls, name: str = "default", *args, **kwargs):
        if name not in cls._instances:
            cls._instances[name] = {}
        
        if cls not in cls._instances[name]:
            with cls._lock:
                if cls not in cls._instances[name]:
                    instance = super().__new__(cls)
                    cls._instances[name][cls] = instance
                    logger.debug(f"创建命名单例实例: {cls.__name__}[{name}]")
        
        return cls._instances[name][cls]
    
    @classmethod
    def get_instance(cls, name: str = "default"):
        """获取指定名称的实例"""
        return cls(name)
    
    @classmethod
    def clear_named_instances(cls, name: str):
        """清除指定名称的所有实例"""
        with cls._lock:
            if name in cls._instances:
                del cls._instances[name]
                logger.debug(f"清除命名单例实例: {name}")


class LazySingleton:
    """延迟初始化的单例"""
    
    _instances: Dict[Type, Any] = {}
    _initialized: Dict[Type, bool] = {}
    _lock = threading.RLock()
    
    def __new__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__new__(cls)
                    cls._instances[cls] = instance
                    cls._initialized[cls] = False
                    logger.debug(f"创建延迟单例实例: {cls.__name__}")
        return cls._instances[cls]
    
    def __init__(self, *args, **kwargs):
        if not self._initialized.get(self.__class__, False):
            with self._lock:
                if not self._initialized.get(self.__class__, False):
                    self._lazy_init(*args, **kwargs)
                    self._initialized[self.__class__] = True
                    logger.debug(f"延迟初始化完成: {self.__class__.__name__}")
    
    def _lazy_init(self, *args, **kwargs):
        """子类需要重写此方法进行实际初始化"""
        pass


class SingletonRegistry:
    """单例注册表，管理所有单例实例"""
    
    def __init__(self):
        self._registry: Dict[str, Any] = {}
        self._lock = threading.RLock()
    
    def register(self, name: str, instance: Any) -> None:
        """注册单例实例"""
        with self._lock:
            if name in self._registry:
                logger.warning(f"单例实例已存在，将被覆盖: {name}")
            self._registry[name] = instance
            logger.debug(f"注册单例实例: {name}")
    
    def get(self, name: str) -> Optional[Any]:
        """获取单例实例"""
        with self._lock:
            return self._registry.get(name)
    
    def unregister(self, name: str) -> bool:
        """注销单例实例"""
        with self._lock:
            if name in self._registry:
                del self._registry[name]
                logger.debug(f"注销单例实例: {name}")
                return True
            return False
    
    def clear_all(self) -> None:
        """清除所有注册的实例"""
        with self._lock:
            count = len(self._registry)
            self._registry.clear()
            logger.debug(f"清除所有注册的单例实例，共 {count} 个")
    
    def get_all_names(self) -> list:
        """获取所有注册的实例名称"""
        with self._lock:
            return list(self._registry.keys())
    
    def get_instance_count(self) -> int:
        """获取注册的实例数量"""
        with self._lock:
            return len(self._registry)


# 全局单例注册表
singleton_registry = SingletonRegistry()


# 装饰器形式的单例
def singleton(cls):
    """单例装饰器"""
    instances = {}
    lock = threading.RLock()
    
    def get_instance(*args, **kwargs):
        if cls not in instances:
            with lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
                    logger.debug(f"创建装饰器单例实例: {cls.__name__}")
        return instances[cls]
    
    return get_instance


def named_singleton(name: str):
    """命名单例装饰器"""
    def decorator(cls):
        def get_instance(*args, **kwargs):
            instance = singleton_registry.get(name)
            if instance is None:
                instance = cls(*args, **kwargs)
                singleton_registry.register(name, instance)
            return instance
        return get_instance
    return decorator


# 示例使用
class ExampleSingleton(metaclass=ThreadSafeSingleton):
    """使用元类的单例示例"""
    
    def __init__(self):
        self.value = 0
        logger.info("ExampleSingleton 初始化")


class ExampleMixinSingleton(SingletonMixin):
    """使用混入的单例示例"""
    
    def __init__(self):
        self.value = 0
        logger.info("ExampleMixinSingleton 初始化")


@singleton
class ExampleDecoratorSingleton:
    """使用装饰器的单例示例"""
    
    def __init__(self):
        self.value = 0
        logger.info("ExampleDecoratorSingleton 初始化")


class ExampleLazySingleton(LazySingleton):
    """延迟初始化单例示例"""
    
    def _lazy_init(self, initial_value=0):
        self.value = initial_value
        logger.info(f"ExampleLazySingleton 延迟初始化，值: {initial_value}")
