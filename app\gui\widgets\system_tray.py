"""
系统托盘组件
提供系统托盘功能，支持最小化到托盘、右键菜单等
"""
from PyQt6.QtWidgets import QSystemTrayIcon, QMenu, QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QIcon, QAction
from loguru import logger
from pathlib import Path


class SystemTray(QObject):
    """系统托盘类"""
    
    # 信号定义
    show_window_requested = pyqtSignal()
    hide_window_requested = pyqtSignal()
    quit_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.init_tray()
        logger.info("系统托盘初始化完成")
    
    def init_tray(self):
        """初始化系统托盘"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            logger.warning("系统不支持系统托盘")
            return
        
        # 创建系统托盘图标
        self.tray_icon = QSystemTrayIcon(self)
        
        # 设置图标
        self.set_tray_icon()
        
        # 创建托盘菜单
        self.create_tray_menu()
        
        # 连接信号
        self.tray_icon.activated.connect(self.on_tray_activated)
        
        # 显示托盘图标
        self.tray_icon.show()
        
        # 设置初始提示
        self.tray_icon.setToolTip("LarkFlow - 飞书智能消息处理系统")
    
    def set_tray_icon(self):
        """设置托盘图标"""
        try:
            import sys

            # 获取可执行文件所在目录
            if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
                # 打包后环境：先尝试exe同目录，再尝试临时目录
                exe_dir = Path(sys.executable).parent
                temp_dir = Path(sys._MEIPASS)
                icon_paths = [
                    exe_dir / "feishu.ico",
                    temp_dir / "feishu.ico",
                    exe_dir / "static" / "feishu.ico"
                ]
            else:
                # 开发环境
                base_path = Path(__file__).parent.parent.parent.parent
                icon_paths = [
                    base_path / "feishu.ico",
                    Path("feishu.ico"),
                    base_path / "static" / "feishu.ico"
                ]

            icon_set = False
            for icon_path in icon_paths:
                if icon_path.exists():
                    self.tray_icon.setIcon(QIcon(str(icon_path)))
                    logger.info(f"使用托盘图标: {icon_path}")
                    icon_set = True
                    break

            if not icon_set:
                # 使用系统默认图标
                if self.parent_window:
                    self.tray_icon.setIcon(self.parent_window.style().standardIcon(
                        self.parent_window.style().StandardPixmap.SP_ComputerIcon))
                    logger.warning("使用系统默认托盘图标")

        except Exception as e:
            logger.error(f"设置托盘图标失败: {str(e)}")
    
    def create_tray_menu(self):
        """创建托盘菜单"""
        tray_menu = QMenu()
        
        # 显示窗口
        show_action = QAction("显示窗口", self)
        show_action.triggered.connect(self.show_window_requested.emit)
        tray_menu.addAction(show_action)
        
        # 隐藏窗口
        hide_action = QAction("隐藏窗口", self)
        hide_action.triggered.connect(self.hide_window_requested.emit)
        tray_menu.addAction(hide_action)
        
        tray_menu.addSeparator()
        
        # 快速功能
        quick_menu = tray_menu.addMenu("快速功能")
        
        # 启动/停止主应用
        self.start_app_action = QAction("启动主应用", self)
        self.start_app_action.triggered.connect(self.start_main_app)
        quick_menu.addAction(self.start_app_action)
        
        self.stop_app_action = QAction("停止主应用", self)
        self.stop_app_action.triggered.connect(self.stop_main_app)
        self.stop_app_action.setEnabled(False)
        quick_menu.addAction(self.stop_app_action)
        
        quick_menu.addSeparator()
        
        # 功能开关
        self.music_action = QAction("音乐提醒", self)
        self.music_action.setCheckable(True)
        self.music_action.triggered.connect(self.toggle_music_notification)
        quick_menu.addAction(self.music_action)
        
        self.phone_action = QAction("电话提醒", self)
        self.phone_action.setCheckable(True)
        self.phone_action.triggered.connect(self.toggle_phone_notification)
        quick_menu.addAction(self.phone_action)
        
        tray_menu.addSeparator()

        # 退出程序
        quit_action = QAction("退出程序", self)
        quit_action.triggered.connect(self.quit_requested.emit)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
    
    def on_tray_activated(self, reason):
        """托盘图标激活处理"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_window_requested.emit()
        elif reason == QSystemTrayIcon.ActivationReason.Trigger:
            # 单击显示菜单（某些系统）
            pass
    
    def start_main_app(self):
        """启动主应用"""
        try:
            from app.core.state_manager import state_manager
            success = state_manager.start_main_application()
            
            if success:
                self.start_app_action.setEnabled(False)
                self.stop_app_action.setEnabled(True)
                self.show_message("LarkFlow", "主应用启动成功", QSystemTrayIcon.MessageIcon.Information)
                logger.info("通过托盘启动主应用")
            else:
                self.show_message("LarkFlow", "主应用启动失败", QSystemTrayIcon.MessageIcon.Warning)
        except Exception as e:
            logger.error(f"托盘启动主应用失败: {str(e)}")
            self.show_message("LarkFlow", f"启动失败: {str(e)}", QSystemTrayIcon.MessageIcon.Critical)
    
    def stop_main_app(self):
        """停止主应用"""
        try:
            from app.core.state_manager import state_manager
            success = state_manager.stop_main_application()
            
            if success:
                self.start_app_action.setEnabled(True)
                self.stop_app_action.setEnabled(False)
                self.show_message("LarkFlow", "主应用停止成功", QSystemTrayIcon.MessageIcon.Information)
                logger.info("通过托盘停止主应用")
            else:
                self.show_message("LarkFlow", "主应用停止失败", QSystemTrayIcon.MessageIcon.Warning)
        except Exception as e:
            logger.error(f"托盘停止主应用失败: {str(e)}")
            self.show_message("LarkFlow", f"停止失败: {str(e)}", QSystemTrayIcon.MessageIcon.Critical)
    
    def toggle_music_notification(self, checked):
        """切换音乐提醒"""
        try:
            from app.core.state_manager import state_manager
            state_manager.set_state('music_notification_enabled', checked)
            
            status = "启用" if checked else "禁用"
            self.show_message("LarkFlow", f"音乐提醒已{status}", QSystemTrayIcon.MessageIcon.Information)
            logger.info(f"通过托盘{status}音乐提醒")
        except Exception as e:
            logger.error(f"托盘切换音乐提醒失败: {str(e)}")
    
    def toggle_phone_notification(self, checked):
        """切换电话提醒"""
        try:
            from app.core.state_manager import state_manager
            state_manager.set_state('phone_notification_enabled', checked)
            
            status = "启用" if checked else "禁用"
            self.show_message("LarkFlow", f"电话提醒已{status}", QSystemTrayIcon.MessageIcon.Information)
            logger.info(f"通过托盘{status}电话提醒")
        except Exception as e:
            logger.error(f"托盘切换电话提醒失败: {str(e)}")
    

    def show_message(self, title, message, icon=QSystemTrayIcon.MessageIcon.Information):
        """显示托盘消息"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.tray_icon.showMessage(title, message, icon, 3000)
    
    def update_status(self, status):
        """更新托盘状态"""
        try:
            # 更新提示文本
            running_status = "运行中" if status.get('running', False) else "已停止"
            main_app_status = "运行中" if status.get('main_app_running', False) else "未启动"
            
            tooltip = f"LarkFlow - {running_status}\n主应用: {main_app_status}"
            self.tray_icon.setToolTip(tooltip)
            
            # 更新菜单状态
            main_app_running = status.get('main_app_running', False)
            self.start_app_action.setEnabled(not main_app_running)
            self.stop_app_action.setEnabled(main_app_running)
            
            # 更新功能开关状态
            self.music_action.setChecked(status.get('music_notification_enabled', True))
            self.phone_action.setChecked(status.get('phone_notification_enabled', True))
            
        except Exception as e:
            logger.error(f"更新托盘状态失败: {str(e)}")
    
    def hide(self):
        """隐藏托盘图标"""
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
    
    def show(self):
        """显示托盘图标"""
        if hasattr(self, 'tray_icon'):
            self.tray_icon.show()
