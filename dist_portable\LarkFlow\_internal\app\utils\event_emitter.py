"""
事件发射器模块
实现事件驱动的状态更新机制，替代定时器轮询
"""
import threading
from typing import Dict, List, Callable, Any
from loguru import logger


class EventEmitter:
    """线程安全的事件发射器"""
    
    def __init__(self):
        self._listeners: Dict[str, List[Callable]] = {}
        self._lock = threading.RLock()
    
    def on(self, event: str, callback: Callable[[Any], None]) -> None:
        """注册事件监听器"""
        with self._lock:
            if event not in self._listeners:
                self._listeners[event] = []
            self._listeners[event].append(callback)
            logger.debug(f"注册事件监听器: {event}")
    
    def off(self, event: str, callback: Callable[[Any], None] = None) -> None:
        """移除事件监听器"""
        with self._lock:
            if event not in self._listeners:
                return
            
            if callback is None:
                # 移除所有监听器
                del self._listeners[event]
                logger.debug(f"移除所有事件监听器: {event}")
            else:
                # 移除特定监听器
                try:
                    self._listeners[event].remove(callback)
                    if not self._listeners[event]:
                        del self._listeners[event]
                    logger.debug(f"移除特定事件监听器: {event}")
                except ValueError:
                    pass
    
    def emit(self, event: str, data: Any = None) -> None:
        """发射事件"""
        with self._lock:
            listeners = self._listeners.get(event, []).copy()
        
        if not listeners:
            return
        
        logger.debug(f"发射事件: {event}, 监听器数量: {len(listeners)}")
        
        for callback in listeners:
            try:
                callback(data)
            except Exception as e:
                logger.error(f"事件回调执行失败 [{event}]: {str(e)}")
    
    def has_listeners(self, event: str) -> bool:
        """检查是否有监听器"""
        with self._lock:
            return event in self._listeners and len(self._listeners[event]) > 0
    
    def get_listener_count(self, event: str) -> int:
        """获取监听器数量"""
        with self._lock:
            return len(self._listeners.get(event, []))
    
    def clear_all(self) -> None:
        """清除所有监听器"""
        with self._lock:
            self._listeners.clear()
            logger.debug("清除所有事件监听器")


class StateEventEmitter(EventEmitter):
    """状态事件发射器，专门用于状态管理"""
    
    # 预定义的事件类型
    STATE_CHANGED = "state_changed"
    WEBSOCKET_CONNECTED = "websocket_connected"
    WEBSOCKET_DISCONNECTED = "websocket_disconnected"
    MESSAGE_RECEIVED = "message_received"
    REPLY_SENT = "reply_sent"
    MUSIC_NOTIFICATION = "music_notification"
    PHONE_NOTIFICATION = "phone_notification"
    ERROR_OCCURRED = "error_occurred"
    CONNECTION_QUALITY_CHANGED = "connection_quality_changed"
    
    def emit_state_change(self, key: str, old_value: Any, new_value: Any) -> None:
        """发射状态变化事件"""
        self.emit(self.STATE_CHANGED, {
            'key': key,
            'old_value': old_value,
            'new_value': new_value
        })
        
        # 发射特定状态的事件
        self.emit(f"state_changed_{key}", {
            'old_value': old_value,
            'new_value': new_value
        })
    
    def emit_websocket_event(self, connected: bool) -> None:
        """发射WebSocket连接事件"""
        event = self.WEBSOCKET_CONNECTED if connected else self.WEBSOCKET_DISCONNECTED
        self.emit(event, {'connected': connected})
    
    def emit_message_event(self, message_data: Dict[str, Any]) -> None:
        """发射消息事件"""
        self.emit(self.MESSAGE_RECEIVED, message_data)
    
    def emit_reply_event(self, reply_data: Dict[str, Any]) -> None:
        """发射回复事件"""
        self.emit(self.REPLY_SENT, reply_data)
    
    def emit_notification_event(self, notification_type: str, data: Dict[str, Any]) -> None:
        """发射通知事件"""
        if notification_type == "music":
            self.emit(self.MUSIC_NOTIFICATION, data)
        elif notification_type == "phone":
            self.emit(self.PHONE_NOTIFICATION, data)
    
    def emit_error_event(self, error_message: str) -> None:
        """发射错误事件"""
        self.emit(self.ERROR_OCCURRED, {'error': error_message})
    
    def emit_connection_quality_event(self, quality: str) -> None:
        """发射连接质量变化事件"""
        self.emit(self.CONNECTION_QUALITY_CHANGED, {'quality': quality})


# 全局状态事件发射器实例
state_event_emitter = StateEventEmitter()
