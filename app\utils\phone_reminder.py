"""
电话提醒模块
用于发送飞书紧急电话提醒
"""
import asyncio
import threading
import time
from typing import List, Optional
import requests
from loguru import logger
from app.config.settings import settings


class PhoneReminder:
    """电话提醒类 - 支持配置热重载"""

    def __init__(self):
        # 只缓存token相关信息，其他配置动态读取
        self.token_cache = None
        self.token_expire_time = 0

    @property
    def app_id(self):
        """动态获取应用ID"""
        return settings.LARK_APP_ID

    @property
    def app_secret(self):
        """动态获取应用密钥"""
        return settings.LARK_APP_SECRET

    @property
    def user_ids(self):
        """动态获取用户ID列表"""
        return settings.PHONE_NOTIFICATION_USER_IDS

    @property
    def message_ids(self):
        """动态获取消息ID列表"""
        return settings.PHONE_NOTIFICATION_MESSAGE_IDS

    @property
    def interval(self):
        """动态获取间隔时间"""
        return settings.PHONE_NOTIFICATION_INTERVAL

    def get_access_token(self) -> Optional[str]:
        """获取飞书访问令牌"""
        try:
            # 检查缓存的token是否还有效
            current_time = time.time()
            if self.token_cache and current_time < self.token_expire_time:
                return self.token_cache

            if not self.app_id or not self.app_secret:
                logger.error("飞书应用ID或密钥未配置")
                return None

            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }

            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0:
                self.token_cache = result.get("tenant_access_token")
                # 设置token过期时间（提前5分钟过期）
                expire_seconds = result.get("expire", 7200) - 300
                self.token_expire_time = current_time + expire_seconds
                logger.info("成功获取飞书访问令牌")
                return self.token_cache
            else:
                logger.error(f"获取访问令牌失败: {result}")
                return None

        except Exception as e:
            logger.error(f"获取访问令牌时出错: {str(e)}")
            return None

    def send_urgent_phone(self, message_id: str, user_ids: List[str]) -> bool:
        """发送紧急电话提醒"""
        try:
            token = self.get_access_token()
            if not token:
                logger.error("无法获取访问令牌，跳过电话提醒")
                return False

            url = f"https://open.feishu.cn/open-apis/im/v1/messages/{message_id}/urgent_phone?user_id_type=union_id"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            payload = {
                "user_id_list": user_ids
            }

            response = requests.patch(url, headers=headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"成功发送电话提醒 - 消息ID: {message_id}, 用户: {user_ids}")
                return True
            else:
                logger.error(f"发送电话提醒失败 - 状态码: {response.status_code}, 响应: {response.text}")
                return False

        except Exception as e:
            logger.error(f"发送电话提醒时出错: {str(e)}")
            return False

    def send_multiple_phone_reminders(self) -> None:
        """发送多个电话提醒（按间隔时间）"""
        if not self.message_ids or not self.user_ids:
            logger.warning("电话提醒配置不完整，跳过发送")
            return

        try:
            logger.info(f"开始发送 {len(self.message_ids)} 个电话提醒，间隔 {self.interval} 秒")
            
            for i, message_id in enumerate(self.message_ids, 1):
                success = self.send_urgent_phone(message_id, self.user_ids)
                
                if success:
                    logger.info(f"电话提醒 {i}/{len(self.message_ids)} 发送成功")
                else:
                    logger.error(f"电话提醒 {i}/{len(self.message_ids)} 发送失败")
                
                # 如果不是最后一个，等待间隔时间
                if i < len(self.message_ids):
                    logger.info(f"等待 {self.interval} 秒后发送下一个提醒...")
                    time.sleep(self.interval)
            
            logger.info("所有电话提醒发送完成")

        except Exception as e:
            logger.error(f"发送多个电话提醒时出错: {str(e)}")

    def start_phone_reminder_async(self) -> threading.Thread:
        """异步启动电话提醒"""
        if not settings.ENABLE_PHONE_NOTIFICATION:
            logger.info("电话提醒功能已禁用")
            return None

        def phone_reminder_task():
            self.send_multiple_phone_reminders()

        reminder_thread = threading.Thread(
            target=phone_reminder_task,
            daemon=True,
            name="PhoneReminderThread"
        )
        reminder_thread.start()
        logger.info("电话提醒线程已启动")
        return reminder_thread

    def is_configured(self) -> bool:
        """检查电话提醒是否已正确配置"""
        return (
            bool(self.app_id) and 
            bool(self.app_secret) and 
            bool(self.user_ids) and 
            bool(self.message_ids)
        )


# 全局电话提醒实例
phone_reminder = PhoneReminder()
