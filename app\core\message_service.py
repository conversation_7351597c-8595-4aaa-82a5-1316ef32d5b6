"""
Service for processing Lark messages
"""
import re
import os
from loguru import logger
from app.config.settings import settings
from app.utils.music_player import music_player
from app.utils.notification import create_combined_notification
from app.utils.phone_reminder import phone_reminder
from app.core.state_manager import state_manager

class MessageService:
    """Service for processing messages"""

    def __init__(self, lark_client):
        self.lark_client = lark_client
        # 初始化组合通知（音乐 + 弹窗）
        self.combined_notification = create_combined_notification(music_player)
        # 跟踪已出现的聊天ID（用于群聊名称匹配模式的首次检测）
        self.seen_chats = set()

    async def process_message(self, user_name, user_id, content, is_group_chat, group_name, chat_id):
        """Process received messages"""
        try:
            # 检查消息监听开关
            if not state_manager.get_state("message_listening_enabled"):
                logger.debug("消息监听已禁用，跳过处理")
                return

            # 更新消息统计
            state_manager.increment_message_count()

            # 记录所有收到的消息（包含群聊名称和发送人名称）
            has_content = content and content.strip()
            if not has_content:
                # 记录非纯文本消息
                if is_group_chat:
                    logger.info(f"收到群聊 {group_name}消息 - 用户: {user_name}, 内容: None")
                else:
                    logger.info(f"收到私聊消息 - 用户: {user_name}, 内容: None")
                logger.warning("收到非纯文本消息，跳过存储")
            else:
                # 记录纯文本消息
                if is_group_chat:
                    logger.info(f"收到群聊 {group_name}消息 - 用户: {user_name}, 内容: {content}")
                else:
                    logger.info(f"收到私聊消息 - 用户: {user_name}, 内容: {content}")

            # 检查消息匹配模式
            match_mode = settings.MESSAGE_MATCH_MODE.lower()
            logger.debug(f"当前消息匹配模式: {match_mode}")
            matched = False
            match_type = ""
            reply_message = ""

            if match_mode == "content":
                # 内容匹配模式 - 需要有文本内容
                logger.debug(f"执行内容匹配模式，消息有内容: {has_content}")
                if has_content:
                    pattern = settings.TRIGGER_PATTERN
                    logger.debug(f"检查内容匹配: 内容='{content}', 模式='{pattern}'")
                    match = re.search(pattern, content.strip(), re.DOTALL)
                    if match:
                        matched = True
                        match_type = "内容匹配"
                        reply_message = settings.REPLY_MESSAGE
                        logger.info(f"内容匹配成功: {content}")
                    else:
                        logger.debug(f"内容不匹配模式")
                else:
                    logger.debug(f"消息无文本内容，跳过内容匹配")

            elif match_mode == "group_name":
                # 群聊名称匹配模式 - 检查群聊名称、消息内容、首次出现
                if is_group_chat:
                    # 检查群聊名称是否匹配模式
                    group_pattern = settings.GROUP_NAME_PATTERN
                    logger.info(f"检查群聊名称匹配: 群聊='{group_name}', 模式='{group_pattern}'")

                    if re.search(group_pattern, group_name):
                        logger.info(f"群聊名称匹配成功: {group_name}")

                        # 检查消息内容是否匹配模式
                        content_pattern = settings.GROUP_NAME_CONTENT_PATTERN

                        # 处理消息内容：如果没有内容，用"None"表示
                        if has_content:
                            content_to_check = content
                        else:
                            content_to_check = "None"  # 非文本消息用"None"表示

                        # 处理空值或None值的情况
                        if not content_pattern or content_pattern.lower() in ['null', '']:
                            content_pattern = ".*"  # 默认匹配所有内容
                            logger.debug(f"内容匹配模式为空，使用默认模式: '.*'")

                        logger.info(f"检查消息内容匹配: 内容='{content_to_check}', 模式='{content_pattern}'")

                        if re.search(content_pattern, content_to_check, re.DOTALL):
                            logger.info(f"消息内容匹配成功")

                            # 检查是否为首次出现的聊天
                            if chat_id not in self.seen_chats:
                                self.seen_chats.add(chat_id)
                                matched = True
                                match_type = "群聊名称匹配"
                                reply_message = settings.GROUP_NAME_REPLY_MESSAGE
                                logger.info(f"首次检测到匹配的群聊: {group_name}, 消息内容: '{content_to_check}'")
                            else:
                                logger.info(f"群聊 {group_name} 已经出现过，跳过回复")
                        else:
                            logger.info(f"群聊 {group_name} 名称匹配但消息内容不匹配，跳过回复")
                    else:
                        logger.info(f"群聊 {group_name} 名称不匹配模式，跳过检查")

            # 如果匹配成功，触发回复
            if matched:
                message_source = f"群聊 {group_name}" if is_group_chat else "私聊"
                logger.info(f"触发{match_type}模式 - {message_source} - 用户: {user_name}")
                await self._handle_function_call(user_name, reply_message, chat_id, is_group_chat, match_type)

        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
            state_manager.set_error(f"处理消息时出错: {str(e)}")


    async def _handle_function_call(self, user_name, reply_message, chat_id, is_group_chat, match_type):
        """处理匹配模式触发的回复请求"""
        try:
            # 并行启动音乐提醒和电话提醒
            self._start_notifications()

            # 检查自动回复开关
            if state_manager.get_state("auto_reply_enabled"):
                # 使用传入的回复消息
                response_text = reply_message

                # 发送消息并检查响应
                response = self.lark_client.send_msg(response_text, chat_id)

                if response.status_code == 200:
                    logger.info(f"成功回复用户 {user_name} ({match_type}): {response_text}")
                    state_manager.increment_reply_count()
                else:
                    logger.error(f"发送消息失败，状态码: {response.status_code}, 响应: {response.text}")
                    state_manager.set_error(f"发送消息失败，状态码: {response.status_code}")
            else:
                logger.info(f"自动回复已禁用，跳过回复用户 {user_name} ({match_type})")

        except Exception as e:
            logger.error(f"处理{match_type}回复时出错: {str(e)}")
            state_manager.set_error(f"处理{match_type}回复时出错: {str(e)}")

            # 如果自动回复开启，尝试发送错误消息
            if state_manager.get_state("auto_reply_enabled"):
                error_msg = f"处理请求时出错: {str(e)}"
                try:
                    self.lark_client.send_msg(error_msg, chat_id)
                except Exception as send_err:
                    logger.error(f"发送错误消息失败: {str(send_err)}")

    def _start_notifications(self):
        """并行启动音乐提醒和电话提醒"""
        # 启动音乐提醒
        self._start_music_notification()

        # 启动电话提醒
        self._start_phone_notification()

    def _start_music_notification(self):
        """启动音乐提醒"""
        # 检查GUI界面开关状态
        gui_enabled = state_manager.get_state("music_notification_enabled")
        # 检查配置文件设置
        config_enabled = settings.ENABLE_MUSIC_NOTIFICATION

        if not gui_enabled:
            logger.info("音乐提醒功能已通过用户界面禁用")
            return

        if not config_enabled:
            logger.info("音乐提醒功能已通过配置文件禁用")
            return

        # 获取当前音乐文件路径（支持实时更新）
        music_file = settings.get_current_music_file()

        # 解析文件路径（支持相对路径）
        resolved_path = music_player._resolve_music_file_path(music_file)
        if not os.path.exists(resolved_path):
            logger.warning(f"配置的音乐文件不存在: {resolved_path}")
            # 尝试使用备用文件
            backup_files = [
                "static/audio/notification.mp3",
                "static/audio/notification.wav",
                "static/audio/alert.mp3",
                "static/audio/alert.wav"
            ]
            found_backup = False
            for backup_file in backup_files:
                backup_resolved = music_player._resolve_music_file_path(backup_file)
                if os.path.exists(backup_resolved):
                    music_file = backup_file
                    logger.info(f"使用备用音乐文件: {backup_file}")
                    found_backup = True
                    break

            if not found_backup:
                error_msg = f"没有找到可用的音乐文件。请检查以下路径：\n- {resolved_path}\n- static/audio/notification.mp3\n- static/audio/notification.wav"
                logger.error(error_msg)
                state_manager.set_error("音乐文件不存在，请在配置页面设置正确的文件路径")
                return

        try:
            # 启动组合通知：音乐播放 + 弹窗提醒
            logger.info(f"启动组合通知: {music_file}")

            # 获取通知标题和消息
            title = settings.NOTIFICATION_TITLE
            message = settings.NOTIFICATION_MESSAGE

            # 启动组合通知
            self.combined_notification.start_notification(music_file, title, message)

            # 更新统计
            state_manager.increment_music_notification_count()
            logger.info(f"组合通知已启动 - 音乐文件: {music_file}")
        except Exception as e:
            logger.error(f"启动组合通知失败: {str(e)}")
            state_manager.set_error(f"启动组合通知失败: {str(e)}")

    def _start_phone_notification(self):
        """启动电话提醒"""
        # 检查GUI界面开关状态
        gui_enabled = state_manager.get_state("phone_notification_enabled")
        # 检查配置文件设置
        config_enabled = settings.ENABLE_PHONE_NOTIFICATION

        if not gui_enabled:
            logger.info("电话提醒功能已通过用户界面禁用")
            return

        if not config_enabled:
            logger.info("电话提醒功能已通过配置文件禁用")
            return

        if not phone_reminder.is_configured():
            logger.warning("电话提醒配置不完整，跳过电话提醒")
            logger.info("请检查以下配置项：LARK_APP_ID, LARK_APP_SECRET, PHONE_NOTIFICATION_USER_IDS, PHONE_NOTIFICATION_MESSAGE_IDS")
            state_manager.set_error("电话提醒配置不完整")
            return

        try:
            # 异步启动电话提醒
            phone_reminder.start_phone_reminder_async()
            logger.info("电话提醒已启动")
            state_manager.increment_phone_notification_count()
        except Exception as e:
            logger.error(f"启动电话提醒失败: {str(e)}")
            state_manager.set_error(f"启动电话提醒失败: {str(e)}")

