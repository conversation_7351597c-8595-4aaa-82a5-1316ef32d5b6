#!/usr/bin/env python3
"""
LarkFlow 便携版打包脚本
创建文件夹形式的便携版，确保没有Python环境的用户也能使用
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

# 项目信息
PROJECT_NAME = "LarkFlow"
VERSION = "2.0.0"
AUTHOR = "LarkFlow Team"

# 打包配置
DIST_DIR = "dist_portable"
BUILD_DIR = "build_temp"

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print(f"🚀 {PROJECT_NAME} 便携版打包工具 v{VERSION}")
    print("=" * 60)
    print(f"📅 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"👨‍💻 开发者: {AUTHOR}")
    print("=" * 60)

def check_environment():
    """检查打包环境"""
    print("\n🔍 检查打包环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查必要文件
    required_files = [
        "main.py",
        "gui_launcher.py", 
        "requirements.txt",
        ".env.example",
        "feishu.ico"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 环境检查通过")
    return True

def clean_build_dirs():
    """清理构建目录"""
    print("\n🧹 清理构建目录...")
    
    dirs_to_clean = [DIST_DIR, BUILD_DIR, "build", "dist"]
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"✅ 已清理: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"✅ 已清理: {spec_file}")

def build_gui_version():
    """构建GUI版本"""
    print("\n� 构建GUI版本...")

    # GUI版本的PyInstaller命令
    gui_cmd = [
        sys.executable, "-m", "PyInstaller",
        "--name=LarkFlow-GUI",
        "--windowed",  # 无控制台窗口
        "--onedir",    # 文件夹模式
        "--clean",
        "--noconfirm",
        f"--icon=feishu.ico",
        "--add-data=static;static",
        "--add-data=app;app",
        "--add-data=builder;builder",
        "--add-data=.env.example;.",
        "--add-data=feishu.ico;.",
        "--add-data=docs;docs",
        "--add-data=README.md;.",
        "--add-data=LICENSE;.",
        "--add-data=CHANGELOG.md;.",
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=PyQt6.sip",
        "--hidden-import=app.gui.main_window",
        "--hidden-import=app.gui.pages.status_page",
        "--hidden-import=app.gui.pages.config_page",
        "--hidden-import=app.gui.pages.log_page",
        "--hidden-import=app.gui.pages.audio_page",
        "--hidden-import=app.gui.styles.style_manager",
        "--hidden-import=app.core.state_manager",
        "--hidden-import=app.core.config_manager",
        "--hidden-import=app.api.auth",
        "--hidden-import=app.api.lark_client",
        "--hidden-import=app.utils.logger_config",
        "--hidden-import=pygame",
        "--hidden-import=loguru",
        "--hidden-import=execjs",
        "--hidden-import=PyExecJS",
        "--hidden-import=protobuf_to_dict",
        "gui_launcher.py"
    ]

    print(f"执行命令: {' '.join(gui_cmd[:5])}...")
    result = subprocess.run(gui_cmd, capture_output=True, text=True)

    if result.returncode != 0:
        print("❌ GUI版本构建失败:")
        print(result.stderr)
        return False

    print("✅ GUI版本构建完成")
    return True

def build_cli_version():
    """构建命令行版本"""
    print("\n🔨 构建命令行版本...")

    # CLI版本的PyInstaller命令
    cli_cmd = [
        sys.executable, "-m", "PyInstaller",
        "--name=LarkFlow-CLI",
        "--console",   # 保留控制台窗口
        "--onedir",    # 文件夹模式
        "--clean",
        "--noconfirm",
        f"--icon=feishu.ico",
        "--add-data=static;static",
        "--add-data=app;app",
        "--add-data=builder;builder",
        "--add-data=.env.example;.",
        "--add-data=feishu.ico;.",
        "--hidden-import=app.api.auth",
        "--hidden-import=app.api.lark_client",
        "--hidden-import=app.core.message_service",
        "--hidden-import=app.core.state_manager",
        "--hidden-import=app.utils.logger_config",
        "--hidden-import=app.utils.lark_utils",
        "--hidden-import=pygame",
        "--hidden-import=loguru",
        "--hidden-import=execjs",
        "--hidden-import=PyExecJS",
        "--hidden-import=protobuf_to_dict",
        "main.py"
    ]

    print(f"执行命令: {' '.join(cli_cmd[:5])}...")
    result = subprocess.run(cli_cmd, capture_output=True, text=True)

    if result.returncode != 0:
        print("❌ CLI版本构建失败:")
        print(result.stderr)
        return False

    print("✅ CLI版本构建完成")
    return True

def build_executable():
    """构建可执行文件"""
    print("\n🔨 开始构建可执行文件...")

    # 先构建GUI版本
    if not build_gui_version():
        return False

    # 再构建CLI版本
    if not build_cli_version():
        return False

    print("✅ 所有版本构建完成")
    return True

def create_portable_package():
    """创建便携版包"""
    print("\n📦 创建便携版包...")

    # 创建便携版目录
    portable_dir = Path(DIST_DIR) / PROJECT_NAME
    portable_dir.mkdir(parents=True, exist_ok=True)

    # 复制GUI版本构建结果
    gui_build_output = Path("dist") / "LarkFlow-GUI"
    if gui_build_output.exists():
        # 复制GUI版本的所有文件到便携版目录
        for item in gui_build_output.iterdir():
            if item.is_file():
                shutil.copy2(item, portable_dir / item.name)
            elif item.is_dir():
                shutil.copytree(item, portable_dir / item.name, dirs_exist_ok=True)
        print(f"✅ 已复制GUI版本到: {portable_dir}")
    else:
        print("❌ GUI版本构建输出目录不存在")
        return False

    # 复制CLI版本的可执行文件
    cli_build_output = Path("dist") / "LarkFlow-CLI"
    if cli_build_output.exists():
        cli_exe = cli_build_output / "LarkFlow-CLI.exe"
        if cli_exe.exists():
            shutil.copy2(cli_exe, portable_dir / "LarkFlow-CLI.exe")
            print("✅ 已复制CLI版本可执行文件")
        else:
            # Linux/Mac版本
            cli_exe = cli_build_output / "LarkFlow-CLI"
            if cli_exe.exists():
                shutil.copy2(cli_exe, portable_dir / "LarkFlow-CLI")
                print("✅ 已复制CLI版本可执行文件")
    else:
        print("⚠️ CLI版本构建输出目录不存在，跳过")

    return True

def create_config_files():
    """创建配置文件和说明"""
    print("\n⚙️ 创建配置文件和说明...")

    portable_dir = Path(DIST_DIR) / PROJECT_NAME

    # 复制配置模板
    shutil.copy2(".env.example", portable_dir / ".env.example")
    print("✅ 已复制配置模板")

    # 创建默认配置文件
    default_env = portable_dir / ".env"
    if not default_env.exists():
        shutil.copy2(".env.example", default_env)
        print("✅ 已创建默认配置文件")

    # 创建启动脚本
    create_startup_scripts(portable_dir)

    # 创建使用说明
    create_user_guide(portable_dir)

def create_startup_scripts(portable_dir):
    """创建启动脚本"""
    print("📝 创建启动脚本...")

    # Windows批处理脚本 - GUI版本
    gui_bat_content = f'''@echo off
chcp 65001 >nul
title {PROJECT_NAME} GUI版本
echo.
echo ========================================
echo   {PROJECT_NAME} GUI版本启动器
echo ========================================
echo.
echo 正在启动GUI界面...
echo.

REM 检查配置文件
if not exist ".env" (
    echo 警告: 未找到配置文件 .env
    echo 将使用默认配置模板创建...
    copy ".env.example" ".env" >nul
    echo.
    echo 请编辑 .env 文件配置飞书Cookie等参数
    echo 然后重新运行此脚本
    echo.
    pause
    exit /b 1
)

REM 启动GUI版本
"LarkFlow-GUI.exe"

if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
    echo 请检查日志文件或联系技术支持
    echo.
    pause
)
'''

    # Windows批处理脚本 - 命令行版本
    cli_bat_content = f'''@echo off
chcp 65001 >nul
title {PROJECT_NAME} 命令行版本
echo.
echo ========================================
echo   {PROJECT_NAME} 命令行版本启动器
echo ========================================
echo.

REM 检查配置文件
if not exist ".env" (
    echo 警告: 未找到配置文件 .env
    echo 将使用默认配置模板创建...
    copy ".env.example" ".env" >nul
    echo.
    echo 请编辑 .env 文件配置飞书Cookie等参数
    echo 然后重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo 正在启动命令行版本...
echo 按 Ctrl+C 可以退出程序
echo.

REM 启动命令行版本
"LarkFlow-CLI.exe"

if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
    echo 请检查日志文件或联系技术支持
    echo.
    pause
)
'''

    # 写入批处理文件
    with open(portable_dir / "启动GUI版本.bat", 'w', encoding='gbk') as f:
        f.write(gui_bat_content)

    with open(portable_dir / "启动命令行版本.bat", 'w', encoding='gbk') as f:
        f.write(cli_bat_content)

    print("✅ 已创建Windows启动脚本")

    # Linux/Mac shell脚本
    shell_script_content = f'''#!/bin/bash

echo "========================================"
echo "  {PROJECT_NAME} 启动器"
echo "========================================"
echo

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "警告: 未找到配置文件 .env"
    echo "将使用默认配置模板创建..."
    cp ".env.example" ".env"
    echo
    echo "请编辑 .env 文件配置飞书Cookie等参数"
    echo "然后重新运行此脚本"
    echo
    read -p "按回车键退出..."
    exit 1
fi

# 选择运行模式
echo "请选择运行模式:"
echo "1) GUI界面版本 (推荐)"
echo "2) 命令行版本"
echo
read -p "请输入选择 (1-2): " choice

case $choice in
    1)
        echo "正在启动GUI版本..."
        ./LarkFlow-GUI
        ;;
    2)
        echo "正在启动命令行版本..."
        echo "按 Ctrl+C 可以退出程序"
        ./LarkFlow-CLI
        ;;
    *)
        echo "无效选择，默认启动GUI版本..."
        ./LarkFlow-GUI
        ;;
esac

if [ $? -ne 0 ]; then
    echo
    echo "程序异常退出，错误代码: $?"
    echo "请检查日志文件或联系技术支持"
    echo
    read -p "按回车键退出..."
fi
'''

    shell_script = portable_dir / "start.sh"
    with open(shell_script, 'w', encoding='utf-8') as f:
        f.write(shell_script_content)

    # 设置执行权限
    try:
        shell_script.chmod(0o755)
        print("✅ 已创建Linux/Mac启动脚本")
    except:
        print("⚠️ 无法设置shell脚本执行权限（Windows系统正常）")

def create_user_guide(portable_dir):
    """创建用户使用指南"""
    print("📖 创建用户使用指南...")

    guide_content = f'''# {PROJECT_NAME} 便携版使用指南

## 🚀 快速开始

### Windows用户
1. **GUI版本（推荐）**: 双击 `启动GUI版本.bat`
2. **命令行版本**: 双击 `启动命令行版本.bat`

### Linux/Mac用户
1. 打开终端，进入程序目录
2. 运行: `./start.sh`
3. 根据提示选择运行模式

## ⚙️ 配置说明

### 首次使用
1. 程序会自动创建 `.env` 配置文件
2. 编辑 `.env` 文件，填入必要配置：
   - `LARK_COOKIE`: 飞书Cookie（必需）
   - 其他配置项根据需要修改

### 获取飞书Cookie
1. 打开浏览器，登录飞书网页版
2. 按F12打开开发者工具
3. 切换到Network标签页
4. 刷新页面，找到任意请求
5. 复制Cookie值到配置文件

## 📁 目录结构

```
{PROJECT_NAME}/
├── LarkFlow-GUI.exe          # GUI版本可执行文件
├── LarkFlow-CLI.exe          # 命令行版本可执行文件
├── 启动GUI版本.bat           # Windows GUI启动脚本
├── 启动命令行版本.bat        # Windows CLI启动脚本
├── start.sh                  # Linux/Mac启动脚本
├── .env                      # 配置文件
├── .env.example              # 配置模板
├── static/                   # 静态资源
│   ├── audio/               # 音频文件目录
│   └── lark_decrypt.js      # 飞书解密脚本
├── logs/                     # 日志目录（自动创建）
└── 使用说明.txt              # 本文件
```

## 🎵 音乐提醒设置

1. 将音乐文件放入 `static/audio/` 目录
2. 支持格式：MP3、WAV、OGG
3. 在 `.env` 文件中设置音乐文件路径：
   ```
   NOTIFICATION_MUSIC_FILE=static/audio/your_music.mp3
   ```

## � Node.js集成说明

本程序采用"JavaScript优先，Python备用"的智能兼容性方案：

### ✅ 优势
- **完美兼容**: 在任何环境下都能正常工作
- **零依赖**: 无需安装Node.js或其他外部软件
- **自动降级**: 智能检测并选择最佳执行方式
- **体积精简**: 无需打包额外的运行时环境

### � 工作原理
- 优先尝试使用系统JavaScript引擎（如果可用）
- 遇到兼容性问题时自动切换到Python实现
- 确保所有核心功能在任何环境下都能正常工作

## �🔧 常见问题

### 1. 程序无法启动
- 检查是否有杀毒软件拦截
- 确认配置文件格式正确
- 查看日志文件获取详细错误信息

### 2. 无法连接飞书
- 检查网络连接
- 确认Cookie是否有效（可能已过期）
- 重新获取Cookie并更新配置

### 3. 音乐无法播放
- 确认音频文件存在且格式支持
- 检查系统音频设备是否正常
- 确认音频文件路径配置正确

### 4. JavaScript执行错误
- 程序会自动使用Python备用方案，通常不会出现此问题
- 如果看到"尝试使用Python实现的备用方案"日志，说明自动降级正常工作
- 查看日志文件获取详细错误信息

### 5. 权限问题（Linux/Mac）
```bash
# 给予执行权限
chmod +x start.sh
chmod +x LarkFlow-GUI
chmod +x LarkFlow-CLI
```

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: GitHub Issues
- 📖 详细文档: 查看docs目录

## 📄 版本信息

- 版本: {VERSION}
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 开发者: {AUTHOR}

---

⭐ 如果这个工具对您有帮助，请给项目一个Star！
'''

    with open(portable_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(guide_content)

    print("✅ 已创建用户使用指南")

def create_audio_directory():
    """创建音频目录和示例文件"""
    print("🎵 创建音频目录...")

    portable_dir = Path(DIST_DIR) / PROJECT_NAME
    audio_dir = portable_dir / "static" / "audio"

    # 确保音频目录存在
    audio_dir.mkdir(parents=True, exist_ok=True)

    # 创建音频目录说明
    readme_content = '''# 音频文件目录

## 📁 目录说明
此目录用于存放音乐提醒文件。

## 🎵 支持格式
- MP3 (推荐)
- WAV
- OGG

## 📝 使用方法
1. 将音乐文件复制到此目录
2. 在 .env 配置文件中设置文件路径：
   ```
   NOTIFICATION_MUSIC_FILE=static/audio/your_music.mp3
   ```

## 💡 建议
- 文件大小：建议小于10MB
- 播放时长：建议10-30秒
- 音量：适中，避免过大或过小

## 🔧 故障排除
如果音乐无法播放：
1. 检查文件格式是否支持
2. 确认文件路径配置正确
3. 检查系统音频设备
4. 查看程序日志获取详细错误信息
'''

    with open(audio_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("✅ 已创建音频目录和说明文件")

def create_logs_directory():
    """创建日志目录"""
    print("📝 创建日志目录...")

    portable_dir = Path(DIST_DIR) / PROJECT_NAME
    logs_dir = portable_dir / "logs"
    logs_dir.mkdir(exist_ok=True)

    # 创建日志目录说明
    log_readme = '''# 日志目录

此目录用于存放程序运行日志。

## 📋 日志文件说明
- `app.log`: 应用程序主日志
- `error.log`: 错误日志
- `gui_launcher.log`: GUI启动器日志

## 🔍 日志查看
- 可以使用文本编辑器打开日志文件
- 日志按时间顺序记录程序运行信息
- 出现问题时，请查看最新的错误日志

## 🧹 日志清理
- 日志文件会自动轮转，避免占用过多空间
- 如需手动清理，可以删除旧的日志文件
'''

    with open(logs_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(log_readme)

    print("✅ 已创建日志目录")

def finalize_package():
    """完善打包"""
    print("\n🎁 完善打包...")

    portable_dir = Path(DIST_DIR) / PROJECT_NAME

    # 创建音频和日志目录
    create_audio_directory()
    create_logs_directory()

    # 使用Python备用方案，无需集成Node.js
    print("✅ 使用Python备用方案，确保JavaScript函数在所有环境下都能正常工作")

    # 复制重要文档（仅保留必要文件）
    docs_to_copy = [
        "LICENSE"  # 只保留许可证文件
    ]

    for doc in docs_to_copy:
        if Path(doc).exists():
            shutil.copy2(doc, portable_dir / doc)
            print(f"✅ 已复制: {doc}")
        else:
            print(f"⚠️ 文件不存在，跳过: {doc}")

    # 设置可执行文件权限（Linux/Mac）
    try:
        for exe_file in ["LarkFlow-GUI", "LarkFlow-CLI"]:
            exe_path = portable_dir / exe_file
            if exe_path.exists():
                exe_path.chmod(0o755)
        print("✅ 已设置可执行文件权限")
    except:
        print("⚠️ 无法设置可执行文件权限（Windows系统正常）")

    print("✅ 打包完善完成")

def print_summary():
    """打印构建总结"""
    print("\n" + "=" * 60)
    print("🎉 便携版打包完成！")
    print("=" * 60)

    portable_dir = Path(DIST_DIR) / PROJECT_NAME

    print(f"📁 输出目录: {portable_dir.absolute()}")
    print(f"📦 包大小: {get_directory_size(portable_dir):.1f} MB")

    print("\n📋 包含文件:")
    print("  ✅ LarkFlow-GUI.exe (GUI版本)")
    print("  ✅ LarkFlow-CLI.exe (命令行版本)")
    print("  ✅ 启动脚本 (Windows + Linux/Mac)")
    print("  ✅ 配置文件和模板")
    print("  ✅ 静态资源和文档")
    print("  ✅ 使用说明")
    print("  ✅ Python备用方案 (确保JavaScript函数在所有环境下正常工作)")

    print("\n🚀 使用方法:")
    print("  Windows: 双击 '启动GUI版本.bat'")
    print("  Linux/Mac: 运行 './start.sh'")

    print("\n⚙️ 首次使用:")
    print("  1. 编辑 .env 文件配置飞书Cookie")
    print("  2. 将音乐文件放入 static/audio/ 目录")
    print("  3. 运行启动脚本")

    print("\n" + "=" * 60)

def get_directory_size(directory):
    """获取目录大小（MB）"""
    total_size = 0
    for dirpath, _, filenames in os.walk(directory):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)  # 转换为MB

# Node.js下载函数已移除 - 使用Python备用方案，无需Node.js

def main():
    """主函数"""
    try:
        # 打印横幅
        print_banner()

        # 检查环境
        if not check_environment():
            print("\n❌ 环境检查失败，请解决上述问题后重试")
            return False

        # 清理构建目录
        clean_build_dirs()

        # 构建可执行文件
        if not build_executable():
            print("\n❌ 构建失败")
            return False

        # 创建便携版包
        if not create_portable_package():
            print("\n❌ 创建便携版包失败")
            return False

        # 创建配置文件和说明
        create_config_files()

        # 完善打包
        finalize_package()

        # 打印总结
        print_summary()

        return True

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断构建过程")
        return False
    except Exception as e:
        print(f"\n❌ 构建过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            if Path(BUILD_DIR).exists():
                shutil.rmtree(BUILD_DIR)
            # 清理spec文件
            for spec_file in Path(".").glob("*.spec"):
                spec_file.unlink()
        except:
            pass

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 打包成功完成！")
        input("\n按回车键退出...")
        sys.exit(0)
    else:
        print("\n💥 打包失败！")
        input("\n按回车键退出...")
        sys.exit(1)
