# LarkFlow 项目结构

## 📁 目录结构

```
LarkFlow/
├── app/                    # 应用程序主目录
│   ├── api/               # API相关模块
│   │   ├── auth.py        # 认证模块
│   │   ├── lark_client.py # 飞书客户端
│   │   └── __init__.py
│   ├── config/            # 配置管理
│   │   ├── settings.py    # 设置管理
│   │   └── __init__.py
│   ├── core/              # 核心业务逻辑
│   │   ├── app_service_manager.py  # 应用服务管理器
│   │   ├── message_service.py      # 消息服务
│   │   ├── state_manager.py        # 状态管理器
│   │   └── __init__.py
│   ├── gui/               # 图形用户界面
│   │   ├── pages/         # 页面组件
│   │   │   ├── audio_page.py    # 音频管理页面
│   │   │   ├── config_page.py   # 配置页面
│   │   │   ├── log_page.py      # 日志页面
│   │   │   ├── status_page.py   # 状态页面
│   │   │   └── __init__.py
│   │   ├── styles/        # 样式文件
│   │   │   ├── modern_style.qss  # 现代样式表
│   │   │   ├── style_manager.py  # 样式管理器
│   │   │   └── __init__.py
│   │   ├── utils/         # GUI工具
│   │   │   ├── animations.py    # 动画效果
│   │   │   └── __init__.py
│   │   ├── widgets/       # 自定义组件
│   │   │   └── __init__.py
│   │   ├── main_window.py # 主窗口
│   │   └── __init__.py
│   ├── utils/             # 工具模块
│   │   ├── error_handler.py     # 错误处理
│   │   ├── event_emitter.py     # 事件发射器
│   │   ├── lark_utils.py        # 飞书工具
│   │   ├── logger_config.py     # 日志配置
│   │   ├── music_player.py      # 音乐播放器
│   │   ├── notification.py      # 通知系统
│   │   ├── phone_reminder.py    # 电话提醒
│   │   ├── resource_manager.py  # 资源管理器
│   │   ├── singleton.py         # 单例模式
│   │   └── __init__.py
│   └── __init__.py
├── 📁 builder/                      # 协议构建器
│   ├── __init__.py
│   ├── header.py                    # 请求头构建
│   ├── params.py                    # 参数构建
│   └── proto.py                     # 协议解析
├── 📁 static/                       # 静态资源
│   ├── 📁 audio/                    # 音频文件
│   │   └── README.md
│   ├── 📁 resource/                 # 图片资源
│   │   ├── back_end.png
│   │   ├── front_end.png
│   │   ├── front_end_1.png
│   │   ├── front_end_2.png
│   │   └── functions.png
│   ├── __init__.py
│   ├── lark_decrypt.js             # 飞书解密工具
│   ├── proto.proto                 # 协议定义文件
│   └── proto_pb2.py                # 生成的协议文件
├── 📁 docs/                         # 文档目录
│   ├── 📁 user/                     # 用户文档
│   │   ├── QUICK_START.md           # 快速开始指南
│   │   ├── WEB_INTERFACE.md         # Web界面文档
│   │   ├── MUSIC_NOTIFICATION.md    # 音乐提醒功能说明
│   │   └── PHONE_NOTIFICATION.md    # 电话提醒功能说明
│   ├── 📁 developer/                # 开发者文档
│   │   ├── DEVELOPMENT.md           # 开发指南
│   │   ├── SECURITY.md              # 安全性文档
│   │   ├── TROUBLESHOOTING.md       # 故障排除指南
│   │   └── QUICK_FIX.md            # 快速修复指南
│   └── 📁 guides/                   # 功能指南
│       ├── WEB_CONTROL_GUIDE.md     # Web控制界面指南
│       ├── WEB_MUSIC_PLAYER_GUIDE.md # Web音乐播放器指南
│       ├── WEB_NOTIFICATION_DIALOG_GUIDE.md # Web通知对话框指南
│       └── AUDIO_FILE_MANAGER_GUIDE.md # 音频文件管理指南

├── 📁 scripts/                      # 工具脚本目录
│   ├── 📁 maintenance/              # 维护脚本
│   │   ├── clean_cache.py           # 清理缓存脚本
│   │   └── quick_fix.py            # 快速修复脚本
│   └── 📁 tools/                    # 工具脚本
│       ├── manual_phone_reminder.py # 手动电话提醒工具
│       └── setup_project.py        # 项目初始化脚本
├── 📁 dist/                         # 打包输出目录
│   ├── LarkFlow.exe                # 可执行文件
│   ├── .env.example                # 配置模板
│   ├── README.txt                  # 使用说明
│   ├── 使用说明.txt                 # 中文说明
│   ├── static/                     # 静态资源
│   └── docs/                       # 文档
├── main.py                          # 程序入口
├── requirements.txt                 # Python 依赖
├── .env.example                     # 环境变量模板
├── .gitignore                       # Git 忽略文件
├── build_exe.py                     # 打包脚本
└── build.bat                        # Windows 打包批处理
```

## 📋 模块功能说明

### 🔧 核心模块 (app/)

#### API 模块 (app/api/)
- **auth.py**: 飞书认证管理，Cookie 解析和验证
- **lark_client.py**: 飞书客户端核心，WebSocket 连接和消息处理

#### 配置模块 (app/config/)
- **settings.py**: 应用配置管理，环境变量读取

#### 核心业务 (app/core/)
- **message_service.py**: 消息处理服务，模式匹配和自动回复

#### 工具模块 (app/utils/)
- **lark_utils.py**: 飞书相关工具函数
- **music_player.py**: 音乐播放功能
- **notification.py**: 桌面通知弹窗
- **phone_reminder.py**: 电话提醒功能

#### Web管理模块 (app/web/)
- **web_server.py**: Flask Web服务器，提供HTTP接口和页面路由
- **config_manager.py**: 配置文件管理，支持.env文件读写
- **state_manager.py**: 应用状态管理，实时状态监控和开关控制
- **templates/**: HTML模板文件，响应式Web界面
- **static/**: 静态资源文件，CSS样式和JavaScript脚本

### 🏗️ 协议构建器 (builder/)
- **header.py**: HTTP 请求头构建
- **params.py**: 请求参数构建  
- **proto.py**: Protocol Buffers 协议解析

### 📦 静态资源 (static/)
- **audio/**: 音频文件存储
- **resource/**: 图片资源
- **proto 相关**: 协议定义和生成文件
- **lark_decrypt.js**: JavaScript 解密工具

### 📚 文档 (docs/)

#### 用户文档 (docs/user/)
- **QUICK_START.md**: 快速开始指南
- **WEB_INTERFACE.md**: Web界面使用说明
- **MUSIC_NOTIFICATION.md**: 音乐提醒功能详细说明
- **PHONE_NOTIFICATION.md**: 电话提醒功能详细说明

#### 开发者文档 (docs/developer/)
- **DEVELOPMENT.md**: 开发指南和说明
- **SECURITY.md**: 安全性文档
- **TROUBLESHOOTING.md**: 故障排除指南
- **QUICK_FIX.md**: 快速修复指南

#### 功能指南 (docs/guides/)
- **WEB_CONTROL_GUIDE.md**: Web控制界面指南
- **WEB_MUSIC_PLAYER_GUIDE.md**: Web音乐播放器指南
- **WEB_NOTIFICATION_DIALOG_GUIDE.md**: Web通知对话框指南
- **AUDIO_FILE_MANAGER_GUIDE.md**: 音频文件管理指南

### 🔧 工具脚本 (scripts/)

#### 维护脚本 (scripts/maintenance/)
- **clean_cache.py**: 清理缓存脚本
- **quick_fix.py**: 快速修复脚本

#### 工具脚本 (scripts/tools/)
- **setup_project.py**: 项目初始化和环境设置
- **clean_cache.py**: 清理项目缓存文件
- **manual_phone_reminder.py**: 手动发送电话提醒工具

### 🚀 构建和部署
- **main.py**: 应用程序入口点
- **requirements.txt**: Python 依赖包列表
- **build_exe.py**: PyInstaller 打包脚本
- **build.bat**: Windows 一键打包批处理
- **.env.example**: 环境变量配置模板

## 🎯 架构特点

### 1. **模块化设计**
- 清晰的功能分离
- 易于维护和扩展
- 符合 Python 项目规范

### 2. **配置外置**
- 环境变量管理
- 用户可自定义配置
- 开发/生产环境分离

### 3. **资源管理**
- 静态资源统一管理
- 音频文件独立目录
- 协议文件版本控制

### 4. **文档完善**
- 功能说明文档
- 使用指南
- 项目结构说明

### 5. **部署友好**
- 一键打包脚本
- 依赖自动管理
- 跨平台支持

## 🔄 数据流向

```
用户消息 → WebSocket → 协议解析 → 消息服务 → 模式匹配 → 音乐提醒 + 自动回复
    ↑                                                              ↓
飞书服务器 ←─────────── HTTP API ←─────────── 认证模块 ←─────────── 配置管理
```

## 📈 扩展建议

### 短期优化
- [ ] 添加日志轮转
- [ ] 增加错误重试机制
- [ ] 优化内存使用

### 中期扩展
- [ ] 支持多账号管理
- [ ] 添加 Web 管理界面
- [ ] 集成更多 AI 模型

### 长期规划
- [ ] 插件系统
- [ ] 分布式部署
- [ ] 云服务集成
